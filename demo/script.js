// 全局状态
let currentUser = null;
let currentEmail = null;
let currentVerificationCode = null;

// 页面切换函数
function showPage(pageId) {
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById(pageId).classList.add('active');
}

function showLogin() {
    document.getElementById('login-form').classList.add('active');
    document.getElementById('register-form').classList.remove('active');
}

function showRegister() {
    document.getElementById('register-form').classList.add('active');
    document.getElementById('login-form').classList.remove('active');
}

function showMain() {
    showPage('main-page');
}

function showAuth() {
    showPage('auth-page');
}

function showProfile() {
    showPage('profile-page');
}

// Toast 通知
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 认证相关函数
async function login() {
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    
    if (!email || !password) {
        showToast('请填写完整信息', 'error');
        return;
    }
    
    // 模拟API调用
    try {
        showToast('登录中...', 'info');
        
        // 模拟登录成功
        setTimeout(() => {
            const token = 'mock-token-' + Date.now();
            currentUser = {
                email: email,
                type: 'vip',
                token: token
            };

            // 保存到本地存储
            localStorage.setItem('auth_token', token);
            localStorage.setItem('user_info', JSON.stringify(currentUser));

            // 更新UI状态
            updateLoginStatus(true, email, 'vip');

            showToast('登录成功！');
            showMain();
        }, 1000);
        
    } catch (error) {
        showToast('登录失败，请重试', 'error');
    }
}

async function register() {
    const username = document.getElementById('register-username').value;
    const domain = document.getElementById('email-domain').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm').value;
    const verificationCode = document.getElementById('verification-code').value;

    // 组合完整邮箱地址
    const email = username && domain ? `${username}@${domain}` : '';

    if (!username || !domain || !password || !confirmPassword || !verificationCode) {
        showToast('请填写完整信息', 'error');
        return;
    }

    // 验证用户名格式
    if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
        showToast('用户名只能包含字母、数字、点号、下划线和连字符', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showToast('两次密码输入不一致', 'error');
        return;
    }
    
    // 模拟API调用
    try {
        showToast('注册中...', 'info');
        
        // 模拟注册成功
        setTimeout(() => {
            const token = 'mock-token-' + Date.now();
            currentUser = {
                email: email,
                type: 'normal',
                token: token
            };

            // 保存到本地存储
            localStorage.setItem('auth_token', token);
            localStorage.setItem('user_info', JSON.stringify(currentUser));

            // 更新UI状态
            updateLoginStatus(true, email, 'normal');

            showToast('注册成功！');
            showMain();
        }, 1000);
        
    } catch (error) {
        showToast('注册失败，请重试', 'error');
    }
}

async function sendVerificationCode() {
    const username = document.getElementById('register-username').value;
    const domain = document.getElementById('email-domain').value;

    // 组合完整邮箱地址
    const email = username && domain ? `${username}@${domain}` : '';

    if (!username || !domain) {
        showToast('请先完整填写邮箱地址', 'error');
        return;
    }

    // 验证用户名格式
    if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
        showToast('用户名格式不正确', 'error');
        return;
    }
    
    showToast('验证码发送中...', 'info');
    
    // 模拟发送验证码
    setTimeout(() => {
        showToast('验证码已发送到您的邮箱');
    }, 1000);
}

function logout() {
    // 清除认证数据
    clearAuthData();
    currentEmail = null;
    currentVerificationCode = null;

    // 更新UI状态
    updateLoginStatus(false);

    // 重置邮箱和验证码显示
    document.getElementById('current-email').textContent = '点击获取邮箱按钮生成邮箱地址';
    document.querySelector('.btn-copy').disabled = true;
    document.getElementById('verification-code-single').innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">📭</div>
            <p>暂无验证码</p>
        </div>
    `;

    showToast('已退出登录');
    // 退出登录后仍然停留在主页面，只是显示未登录状态
}

// 主要功能函数
async function getEmail() {
    if (!currentUser) {
        showToast('请先登录', 'error');
        return;
    }
    
    showToast('获取邮箱中...', 'info');
    
    // 模拟API调用
    setTimeout(() => {
        const randomEmail = `temp${Math.random().toString(36).substr(2, 8)}@tempmail.com`;
        currentEmail = randomEmail;
        
        document.getElementById('current-email').textContent = randomEmail;
        document.querySelector('.btn-copy').disabled = false;
        
        showToast('邮箱获取成功！');
        
        // 模拟接收验证码
        setTimeout(() => {
            generateNewVerificationCode();
        }, 3000);
    }, 1000);
}

function copyEmail() {
    if (currentEmail) {
        navigator.clipboard.writeText(currentEmail).then(() => {
            showToast('邮箱已复制到剪贴板');
        }).catch(() => {
            showToast('复制失败', 'error');
        });
    }
}

function copyCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        showToast('验证码已复制到剪贴板');
    }).catch(() => {
        showToast('复制失败', 'error');
    });
}

function copyCurrentCode() {
    if (currentVerificationCode) {
        copyCode(currentVerificationCode.code);
    }
}

async function refreshCodes() {
    if (!currentUser) {
        showToast('请先登录', 'error');
        return;
    }

    showToast('刷新中...', 'info');

    // 模拟API调用
    setTimeout(() => {
        if (Math.random() > 0.3) {
            generateNewVerificationCode();
        }
        showToast('刷新完成');
    }, 1000);
}

function generateNewVerificationCode() {
    const code = Math.random().toString().substr(2, 6);
    const time = new Date().toLocaleTimeString();

    currentVerificationCode = {
        code: code,
        time: time,
        id: Date.now()
    };

    updateVerificationCodeUI();
}

function updateVerificationCodeUI() {
    const container = document.getElementById('verification-code-single');

    if (!currentVerificationCode) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📭</div>
                <p>暂无验证码</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="code-item">
            <div class="code-content">
                <div class="code-text">${currentVerificationCode.code}</div>
                <div class="code-time">收到时间: ${currentVerificationCode.time}</div>
            </div>
            <div class="code-actions">
                <button class="btn btn-copy" onclick="copyCurrentCode()">复制</button>
            </div>
        </div>
    `;
}

async function resetPlugin() {
    if (confirm('确定要重置插件吗？这将清除所有数据。')) {
        showToast('重置中...', 'info');
        
        setTimeout(() => {
            logout();
            showToast('插件已重置');
        }, 1000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 模拟加载公告
    setTimeout(() => {
        document.getElementById('announcement').innerHTML = `
            <p>🎉 欢迎使用临时邮箱插件！</p>
            <p>📧 支持接收各种网站的验证码邮件</p>
            <p>⭐ VIP用户享有更长的邮箱有效期和优先支持</p>
            <p>🔄 验证码会自动刷新，也可手动刷新获取最新邮件</p>
        `;
    }, 500);
});

// 模拟定时刷新验证码
setInterval(() => {
    if (currentUser && currentEmail && Math.random() > 0.7) {
        generateNewVerificationCode();
    }
}, 30000); // 每30秒检查一次

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 显示VIP套餐页面
function showVipPlans() {
    showPage('vip-plans-page');
}

// 显示支付页面
function showPayment() {
    showPage('payment-page');

    // 生成订单号
    const orderNumber = generateOrderNumber();
    document.getElementById('order-number').textContent = orderNumber;

    // 开始倒计时
    startCountdown();
}

// 选择套餐
function selectPlan(planType) {
    let productName, amount;

    if (planType === 'week') {
        productName = 'Augment Star 周卡无限会员';
        amount = '¥9.9';
    } else if (planType === 'month') {
        productName = 'Augment Star 月卡无限会员';
        amount = '¥29.9';
    }

    // 更新支付页面信息
    document.getElementById('order-product').textContent = productName;
    document.getElementById('order-amount').textContent = amount;

    showPayment();
}

// 生成订单号
function generateOrderNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    return `${year}${month}${day}${hour}${minute}${second}${random}`;
}

// 开始倒计时
function startCountdown() {
    let timeLeft = 5 * 60; // 5分钟

    const countdownElement = document.getElementById('countdown');

    const timer = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;

        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        if (timeLeft <= 0) {
            clearInterval(timer);
            countdownElement.textContent = '已过期';
        }

        timeLeft--;
    }, 1000);
}

// 确认支付
function confirmPayment() {
    showToast('支付成功！会员已开通', 'success');
    setTimeout(() => {
        showProfile();
    }, 2000);
}

// 取消支付
function cancelPayment() {
    showVipPlans();
}

// 显示介绍页面
function showIntroduction() {
    showToast('介绍页面开发中...', 'info');
}

// 显示FAQ页面
function showFAQ() {
    showToast('FAQ页面开发中...', 'info');
}

// 显示联系页面
function showContact() {
    showToast('联系页面开发中...', 'info');
}

// 更新登录状态UI
function updateLoginStatus(isLoggedIn, email = '', userType = 'normal') {
    const loginPrompt = document.getElementById('login-prompt');
    const profileContainer = document.getElementById('profile-container');
    const vipBadge = document.getElementById('vip-badge');

    if (isLoggedIn) {
        // 已登录状态
        loginPrompt.style.display = 'none';
        profileContainer.style.display = 'flex';

        // 更新VIP状态
        if (userType === 'vip') {
            vipBadge.style.display = 'flex';
        } else {
            vipBadge.style.display = 'none';
        }

        // 更新个人中心邮箱显示
        const profileEmail = document.getElementById('profile-email');
        if (profileEmail) {
            profileEmail.textContent = email;
        }
    } else {
        // 未登录状态
        loginPrompt.style.display = 'flex';
        profileContainer.style.display = 'none';
        vipBadge.style.display = 'none';
    }
}

// 打开在线教程
function openTutorial() {
    // 这里可以替换为实际的教程网址
    const tutorialUrl = 'https://docs.example.com/tutorial';

    // 在新标签页中打开教程
    window.open(tutorialUrl, '_blank');

    // 显示提示消息
    showToast('正在打开在线教程...', 'info');
}

// Token验证和登录状态管理
async function validateToken() {
    const token = localStorage.getItem('auth_token');
    const userInfo = localStorage.getItem('user_info');

    if (!token || !userInfo) {
        return false;
    }

    try {
        // 模拟Token验证API调用
        const response = await mockValidateToken(token);

        if (response.valid) {
            // Token有效，恢复用户状态
            const user = JSON.parse(userInfo);
            currentUser = user;
            return true;
        } else {
            // Token无效，清除本地存储
            clearAuthData();
            return false;
        }
    } catch (error) {
        return false;
    }
}

// 模拟Token验证API
async function mockValidateToken(token) {
    return new Promise((resolve) => {
        setTimeout(() => {
            // 模拟Token验证逻辑
            const tokenAge = Date.now() - parseInt(token.split('-')[2]);
            const isValid = tokenAge < 24 * 60 * 60 * 1000; // 24小时有效期

            resolve({
                valid: isValid,
                message: isValid ? 'Token有效' : 'Token已过期'
            });
        }, 300);
    });
}

// 清除认证数据
function clearAuthData() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    currentUser = null;
}

// 处理Token无效的情况
function handleTokenInvalid() {
    clearAuthData();
    updateLoginStatus(false);
    showToast('登录已过期，请重新登录', 'error');
}

// 模拟API请求（带Token验证）
async function apiRequest(endpoint, options = {}) {
    const token = localStorage.getItem('auth_token');

    if (!token) {
        throw new Error('未登录');
    }

    try {
        // 模拟API调用
        const response = await new Promise((resolve, reject) => {
            setTimeout(() => {
                // 模拟Token验证失败的情况
                if (Math.random() < 0.1) { // 10%概率模拟Token失效
                    reject({ code: 401, message: 'Token无效' });
                    return;
                }

                // 模拟成功响应
                resolve({ success: true, data: {} });
            }, 500);
        });

        return response;
    } catch (error) {
        if (error.code === 401) {
            // Token无效，处理登录过期
            handleTokenInvalid();
        }
        throw error;
    }
}

// 初始化应用状态
async function initializeApp() {
    const isLoggedIn = await validateToken();

    if (isLoggedIn && currentUser) {
        updateLoginStatus(true, currentUser.email, currentUser.type);
    } else {
        updateLoginStatus(false);
    }
}
