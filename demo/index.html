<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱插件</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 登录/注册页面 -->
        <div id="auth-page" class="page">
            <div class="auth-container">
                <div class="logo">
                    <h1>Augment Star</h1>
                </div>

                <!-- 登录表单 -->
                <div id="login-form" class="auth-form active">
                    <h2>登录</h2>
                    <div class="input-group">
                        <input type="email" id="login-email" placeholder="邮箱地址" required>
                    </div>
                    <div class="input-group">
                        <input type="password" id="login-password" placeholder="密码" required>
                    </div>
                    <button class="btn btn-primary" onclick="login()">登录</button>
                    <p class="switch-text">还没有账号？<a href="#" onclick="showRegister()">立即注册</a></p>
                </div>
                
                <!-- 注册表单 -->
                <div id="register-form" class="auth-form">
                    <h2>注册</h2>
                    <div class="input-group">
                        <label class="input-label">邮箱地址</label>
                        <div class="email-input-group">
                            <input type="text" id="register-username" placeholder="用户名" required>
                            <span class="email-separator">@</span>
                            <select id="email-domain" class="domain-select" required>
                                <option value="">选择域名</option>
                                <option value="gmail.com">gmail.com</option>
                                <option value="outlook.com">outlook.com</option>
                                <option value="yahoo.com">yahoo.com</option>
                                <option value="hotmail.com">hotmail.com</option>
                                <option value="qq.com">qq.com</option>
                                <option value="163.com">163.com</option>
                                <option value="126.com">126.com</option>
                                <option value="sina.com">sina.com</option>
                            </select>
                        </div>
                    </div>
                    <div class="input-group">
                        <input type="password" id="register-password" placeholder="密码" required>
                    </div>
                    <div class="input-group">
                        <input type="password" id="register-confirm" placeholder="确认密码" required>
                    </div>
                    <div class="verification-section">
                        <div class="input-group">
                            <input type="text" id="verification-code" placeholder="验证码" required>
                        </div>
                        <button class="btn btn-secondary" onclick="sendVerificationCode()">发送验证码</button>
                    </div>
                    <button class="btn btn-primary" onclick="register()">注册</button>
                    <p class="switch-text">已有账号？<a href="#" onclick="showLogin()">立即登录</a></p>
                </div>
            </div>
        </div>

        <!-- 主页面 -->
        <div id="main-page" class="page active">
            <div class="header">
                <div class="app-title">
                    <h1>Augment Star</h1>
                </div>
                <div class="header-actions">
                    <!-- 未登录状态 -->
                    <div class="login-prompt" id="login-prompt">
                        <button class="btn btn-login" onclick="showAuth()">登录</button>
                    </div>
                    <!-- 已登录状态 -->
                    <div class="profile-icon-container" id="profile-container" style="display: none;">
                        <button class="btn btn-icon" onclick="showProfile()" title="个人中心">👤</button>
                        <div class="vip-badge-v" id="vip-badge" title="VIP会员" style="display: none;">V</div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <!-- 邮箱获取区域 -->
                <div class="card">
                    <div class="card-header">
                        <h3>邮箱地址</h3>
                    </div>
                    <div class="email-display">
                        <div class="email-item">
                            <span class="email-text" id="current-email">点击获取邮箱按钮生成邮箱地址</span>
                            <button class="btn btn-copy" onclick="copyEmail()" disabled>复制</button>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="getEmail()">获取邮箱</button>
                        <button class="btn btn-danger" onclick="resetPlugin()">重置插件</button>
                    </div>
                </div>

                <!-- 验证码区域 -->
                <div class="card">
                    <div class="card-header">
                        <h3>验证码</h3>
                        <button class="btn btn-secondary" onclick="refreshCodes()">刷新</button>
                    </div>
                    <div class="verification-code-single" id="verification-code-single">
                        <div class="empty-state">
                            <div class="empty-icon">📭</div>
                            <p>暂无验证码</p>
                        </div>
                    </div>
                </div>

                <!-- 公告区域 -->
                <div class="card">
                    <div class="card-header">
                        <h3>系统公告</h3>
                        <button class="btn btn-tutorial" onclick="openTutorial()">
                            <span class="tutorial-icon">📚</span>
                            在线教程
                        </button>
                    </div>
                    <div class="announcement" id="announcement">
                        <p>欢迎使用临时邮箱插件！</p>
                        <p>VIP用户享有更长的邮箱有效期和更多功能。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" onclick="showMain()">← 返回</button>
                <h2>个人信息</h2>
                <button class="btn btn-upgrade" onclick="showVipPlans()">续费</button>
            </div>

            <div class="profile-content">
                <!-- 用户信息卡片 -->
                <div class="user-info-card">
                    <div class="user-avatar">👤</div>
                    <div class="user-info-details">
                        <h3 id="profile-email"><EMAIL></h3>
                        <p class="vip-expiry">VIP 到期时间: 2025-10-17</p>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="menu-section">
                    <div class="menu-item" onclick="showIntroduction()">
                        <span>介绍</span>
                        <span class="menu-arrow">查看</span>
                    </div>
                    <div class="menu-item" onclick="showFAQ()">
                        <span>帮助/FAQ</span>
                        <span class="menu-arrow">查看</span>
                    </div>
                    <div class="menu-item" onclick="showContact()">
                        <span>联系我</span>
                        <span class="menu-arrow">查看</span>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="version-section">
                    <div class="version-item">
                        <span>版本号</span>
                        <div class="version-info">
                            <span>v3.2.0</span>
                            <button class="btn btn-check-update">检查更新</button>
                        </div>
                    </div>
                </div>

                <!-- 退出登录 -->
                <div class="logout-section">
                    <div class="logout-item" onclick="logout()">
                        <span>退出登录</span>
                        <button class="btn btn-logout">退出</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP套餐选择页面 -->
        <div id="vip-plans-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" onclick="showProfile()">← 返回</button>
                <h2>选择VIP套餐</h2>
            </div>

            <div class="vip-plans-content">
                <div class="plans-container">
                    <!-- 周卡套餐 -->
                    <div class="plan-card">
                        <h3>周卡无限会员</h3>
                        <div class="plan-price">
                            <span class="current-price">¥9.9</span>
                            <span class="original-price">¥16.9</span>
                        </div>
                        <div class="plan-duration">7天</div>
                        <div class="plan-discount">节省 41%</div>

                        <div class="plan-features">
                            <div class="feature-item">✓ 7天无限次高效快速请求</div>
                            <div class="feature-item">✓ 最高优先级</div>
                            <div class="feature-item">✓ 技术支持</div>
                            <div class="feature-item">✓ 专属客服</div>
                        </div>

                        <button class="btn btn-select-plan" onclick="selectPlan('week')">选择此套餐</button>
                    </div>

                    <!-- 月卡套餐 -->
                    <div class="plan-card featured">
                        <div class="plan-badge">限时特惠</div>
                        <h3>月卡无限会员</h3>
                        <div class="plan-price">
                            <span class="current-price">¥29.9</span>
                            <span class="original-price">¥59.9</span>
                        </div>
                        <div class="plan-duration">每月</div>
                        <div class="plan-discount">节省 49%</div>

                        <div class="plan-features">
                            <div class="feature-item">✓ 30天无限次高效快速请求</div>
                            <div class="feature-item">✓ 最高优先级</div>
                            <div class="feature-item">✓ 技术支持</div>
                            <div class="feature-item">✓ 专属客服</div>
                        </div>

                        <button class="btn btn-select-plan" onclick="selectPlan('month')">选择此套餐</button>
                    </div>
                </div>

                <!-- 温馨提示 -->
                <div class="tips-section">
                    <div class="tips-icon">✓</div>
                    <div class="tips-content">
                        <h4>温馨提示</h4>
                        <p>有任何问题，随时联系我们</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付页面 -->
        <div id="payment-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" onclick="showVipPlans()">← 返回</button>
                <h2>订单支付</h2>
            </div>

            <div class="payment-content">
                <!-- 订单信息 -->
                <div class="order-info">
                    <div class="order-item">
                        <span>商品名称</span>
                        <span id="order-product">Augment Star 周卡无限会员</span>
                    </div>
                    <div class="order-item">
                        <span>支付金额</span>
                        <span class="order-price" id="order-amount">¥9.9</span>
                    </div>
                    <div class="order-item">
                        <span>订单编号</span>
                        <span id="order-number">202508021808302820006</span>
                    </div>
                </div>

                <!-- 支付说明 -->
                <div class="payment-instruction">
                    <p>请使用支付宝扫描下方二维码完成支付</p>
                </div>

                <!-- 二维码 -->
                <div class="qr-section">
                    <div class="qr-code">
                        <div class="qr-placeholder">二维码</div>
                    </div>
                    <div class="qr-timer">二维码有效期: <span id="countdown">4:55</span></div>
                </div>

                <!-- 支付提示 -->
                <div class="payment-notice">
                    <p>支付完成后系统将自动为您开通会员</p>
                </div>

                <!-- 操作按钮 -->
                <div class="payment-actions">
                    <button class="btn btn-primary" onclick="confirmPayment()">我已完成支付</button>
                    <button class="btn btn-secondary" onclick="cancelPayment()">取消支付</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="toast"></div>

    <script src="script.js"></script>
</body>
</html>
