* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #0d1117 0%, #161b22 50%, #21262d 100%);
    min-height: 100vh;
    color: #f0f6fc;
    line-height: 1.6;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    min-height: 100vh;
    background: #0d1117;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    position: relative;
    border-radius: 0;
    border: 1px solid #30363d;
}

/* 页面切换 */
.page {
    display: none;
    min-height: 100vh;
}

.page.active {
    display: block;
}

/* 认证页面样式 */
.auth-container {
    padding: 40px 40px;
    text-align: center;
}

.logo {
    margin-bottom: 40px;
}

.logo h1 {
    font-size: 28px;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
    letter-spacing: 0.5px;
}



.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 32px;
    color: #f0f6fc;
}

.input-group {
    margin-bottom: 20px;
    text-align: left;
}

.input-group input {
    width: 100%;
    padding: 16px;
    border: 1px solid #30363d;
    border-radius: 12px;
    font-size: 16px;
    background: #21262d;
    color: #f0f6fc;
}

.input-group input:focus {
    outline: none;
    border-color: #58a6ff;
    background: #161b22;
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.15);
}

.input-group input::placeholder {
    color: #8b949e;
}

/* 邮箱输入组合样式 */
.input-label {
    display: block;
    color: #f0f6fc;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.email-input-group {
    display: flex;
    align-items: center;
    background: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 0;
    transition: border-color 0.2s;
}

.email-input-group:focus-within {
    border-color: #58a6ff;
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
}

.email-input-group input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 16px;
    color: #f0f6fc;
    font-size: 14px;
    outline: none;
}

.email-input-group input:focus {
    box-shadow: none;
}

.email-separator {
    color: #7d8590;
    font-size: 16px;
    font-weight: 500;
    padding: 0 8px;
    user-select: none;
}

.domain-select {
    background: transparent;
    border: none;
    color: #f0f6fc;
    font-size: 14px;
    padding: 12px 16px;
    outline: none;
    cursor: pointer;
    min-width: 140px;
}

.domain-select option {
    background: #21262d;
    color: #f0f6fc;
    padding: 8px;
}

.domain-select:focus {
    background: #30363d;
}

/* 下拉箭头样式 */
.domain-select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237d8590' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.domain-select:hover {
    background-color: #30363d;
}

/* 选项样式优化 */
.domain-select option:hover {
    background: #58a6ff;
}

.verification-section {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.verification-section .input-group {
    flex: 1;
    margin-bottom: 0;
}

/* 按钮样式 */
.btn {
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #238636 0%, #2ea043 100%);
    color: white;
    width: 100%;
    margin: 24px 0;
    border: none;
    box-shadow: 0 4px 12px rgba(35, 134, 54, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1f6f2a 0%, #238636 100%);
}

.btn-secondary {
    background: #21262d;
    color: #f0f6fc;
    border: 1px solid #30363d;
    padding: 12px 20px;
    font-size: 14px;
}

.btn-secondary:hover {
    background: #30363d;
    border-color: #484f58;
}

.btn-copy {
    background: #58a6ff;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    min-width: 60px;
    border: none;
    box-shadow: 0 2px 8px rgba(88, 166, 255, 0.3);
}

.btn-copy:hover {
    background: #388bfd;
}

.btn-copy:disabled {
    background: #484f58;
    cursor: not-allowed;
    box-shadow: none;
}

.btn-icon {
    background: transparent;
    border: 1px solid #30363d;
    padding: 8px;
    border-radius: 8px;
    font-size: 16px;
    width: 40px;
    height: 40px;
    color: #f0f6fc;
}

.btn-icon:hover {
    background: #21262d;
    border-color: #484f58;
}

.btn-back {
    background: transparent;
    color: #58a6ff;
    padding: 8px 16px;
    font-size: 16px;
    border: none;
}

.btn-danger {
    background: #da3633;
    color: white;
    width: 100%;
    border: none;
    box-shadow: 0 4px 12px rgba(218, 54, 51, 0.3);
}

.btn-danger:hover {
    background: #b91c1c;
}

.switch-text {
    color: #8b949e;
    font-size: 14px;
    margin-top: 20px;
}

.switch-text a {
    color: #58a6ff;
    text-decoration: none;
    font-weight: 500;
}

/* 主页面样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #161b22;
    border-bottom: 1px solid #30363d;
    backdrop-filter: blur(20px);
    position: relative;
}



/* 个人中心图标容器 */
.profile-icon-container {
    position: relative;
    display: inline-block;
}

/* VIP徽章V样式 */
.vip-badge-v {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 16px;
    height: 16px;
    background: #ffd700;
    color: #1a1a1a;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #161b22;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
    display: none;
}

.user-type {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-type.vip {
    background: linear-gradient(135deg, #ffd700, #ffb000);
    color: #1a1a1a;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.user-type.normal {
    background: #30363d;
    color: #8b949e;
    border: 1px solid #484f58;
}

/* 应用标题 */
.app-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
}

.app-title h1 {
    font-size: 18px;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
    letter-spacing: 0.5px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 登录提示样式 */
.login-prompt {
    display: flex;
    align-items: center;
}

.btn-login {
    background: #238636;
    color: white;
    padding: 6px 16px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-login:hover {
    background: #2ea043;
}

.main-content {
    padding: 20px;
    background: #0d1117;
    min-height: calc(100vh - 80px);
}

/* 卡片样式 */
.card {
    background: rgba(22, 27, 34, 0.8);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid #30363d;
    backdrop-filter: blur(20px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #f0f6fc;
}

/* 在线教程按钮 */
.btn-tutorial {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.btn-tutorial:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tutorial-icon {
    font-size: 16px;
}

.email-display {
    background: rgba(13, 17, 23, 0.6);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #30363d;
    margin-bottom: 12px;
}

.email-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.email-text {
    flex: 1;
    font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    color: #f0f6fc;
    word-break: break-all;
    font-weight: 500;
}

/* 按钮组样式 */
.action-buttons {
    display: flex;
    gap: 10px;
}

.action-buttons .btn {
    flex: 1;
    margin: 0;
}

/* 单个验证码显示样式 */
.verification-code-single {
    min-height: 48px;
}

.verification-code-single .empty-state {
    padding: 10px 16px;
}

.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: rgba(13, 17, 23, 0.6);
    border-radius: 12px;
    border: 1px solid #30363d;
    border-left: 4px solid #58a6ff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.code-content {
    flex: 1;
}

.code-text {
    font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
    font-size: 18px;
    font-weight: 700;
    color: #58a6ff;
    margin-bottom: 3px;
    letter-spacing: 2px;
}

.code-time {
    font-size: 12px;
    color: #8b949e;
}

.code-actions {
    display: flex;
    gap: 6px;
}

.empty-state {
    text-align: center;
    padding: 10px 16px;
    color: #6e7681;
}

.empty-icon {
    font-size: 20px;
    margin-bottom: 3px;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

.announcement {
    background: rgba(88, 166, 255, 0.1);
    border-left: 4px solid #58a6ff;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(88, 166, 255, 0.2);
}

.announcement p {
    margin-bottom: 8px;
    color: #f0f6fc;
}

.announcement p:last-child {
    margin-bottom: 0;
}

/* 个人中心样式 */
.profile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: #161b22;
    border-bottom: 1px solid #30363d;
    backdrop-filter: blur(20px);
}

.profile-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #f0f6fc;
    flex: 1;
    text-align: center;
    margin: 0 20px;
}

.btn-upgrade {
    background: #6366f1;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 8px;
    border: none;
}

.btn-upgrade:hover {
    background: #5b21b6;
}

.profile-content {
    padding: 20px;
    background: #0d1117;
    min-height: calc(100vh - 80px);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #238636 0%, #2ea043 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 12px rgba(35, 134, 54, 0.4);
}

.user-details h3 {
    font-size: 18px;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 4px;
}

.package-info {
    space-y: 12px;
}

.package-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #30363d;
    color: #f0f6fc;
}

.package-item:last-child {
    border-bottom: none;
}

.package-value {
    font-weight: 600;
    color: #58a6ff;
}

.qr-section {
    text-align: center;
}

.qr-code {
    width: 120px;
    height: 120px;
    background: #21262d;
    border: 2px dashed #484f58;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: #6e7681;
    font-size: 14px;
}

.qr-section p {
    color: #8b949e;
}

/* Toast 通知 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #238636;
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border: 1px solid #2ea043;
    backdrop-filter: blur(20px);
    display: none;
}

.toast.show {
    display: block;
}

.toast.error {
    background: #da3633;
    border-color: #f85149;
}

.toast.info {
    background: #58a6ff;
    border-color: #79c0ff;
}



/* 用户信息卡片 */
.user-info-card {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: #30363d;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #7d8590;
}

.user-info-details h3 {
    margin: 0 0 8px 0;
    color: #f0f6fc;
    font-size: 18px;
    font-weight: 600;
}

.vip-expiry {
    margin: 0;
    color: #7d8590;
    font-size: 14px;
}

/* 菜单部分 */
.menu-section {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #30363d;
    cursor: pointer;
    transition: background-color 0.2s;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #21262d;
}

.menu-item span:first-child {
    color: #f0f6fc;
    font-size: 16px;
}

.menu-arrow {
    color: #7d8590;
    font-size: 14px;
    padding: 4px 12px;
    background: #30363d;
    border-radius: 6px;
}

/* 版本信息 */
.version-section {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    margin-bottom: 20px;
}

.version-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
}

.version-item span:first-child {
    color: #f0f6fc;
    font-size: 16px;
}

.version-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.version-info span {
    color: #7d8590;
    font-size: 14px;
}

.btn-check-update {
    background: #30363d;
    color: #7d8590;
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
}

.btn-check-update:hover {
    background: #484f58;
}

/* 退出登录 */
.logout-section {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
}

.logout-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
}

.logout-item span {
    color: #f0f6fc;
    font-size: 16px;
}

.btn-logout {
    background: #da3633;
    color: white;
    padding: 6px 16px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
}

.btn-logout:hover {
    background: #b91c1c;
}

/* VIP套餐页面样式 */
.vip-plans-content {
    padding: 20px;
    background: #0d1117;
    min-height: calc(100vh - 80px);
}

.plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.plan-card {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    padding: 20px;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.plan-card.featured {
    border-color: #6366f1;
    box-shadow: 0 0 0 1px #6366f1;
}

.plan-badge {
    position: absolute;
    top: -8px;
    right: 20px;
    background: #6366f1;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.plan-card h3 {
    color: #f0f6fc;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.plan-price {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 6px;
}

.current-price {
    color: #6366f1;
    font-size: 32px;
    font-weight: 700;
}

.original-price {
    color: #7d8590;
    font-size: 16px;
    text-decoration: line-through;
}

.plan-duration {
    color: #7d8590;
    font-size: 14px;
    margin-bottom: 6px;
}

.plan-discount {
    color: #238636;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
}

.plan-features {
    margin-bottom: 20px;
}

.feature-item {
    color: #7d8590;
    font-size: 14px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-item:before {
    content: "✓";
    color: #238636;
    font-weight: bold;
}

.btn-select-plan {
    width: 100%;
    background: #6366f1;
    color: white;
    padding: 12px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-select-plan:hover {
    background: #5b21b6;
}

/* 温馨提示 */
.tips-section {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.tips-icon {
    width: 40px;
    height: 40px;
    background: #238636;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.tips-content h4 {
    color: #f0f6fc;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.tips-content p {
    color: #7d8590;
    font-size: 14px;
    margin: 0;
}

/* 支付页面样式 */
.payment-content {
    padding: 20px;
    background: #0d1117;
    min-height: calc(100vh - 80px);
}

.order-info {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #30363d;
    color: #f0f6fc;
}

.order-item:last-child {
    border-bottom: none;
}

.order-item span:first-child {
    color: #7d8590;
}

.order-price {
    color: #6366f1;
    font-size: 18px;
    font-weight: 700;
}

.payment-instruction {
    text-align: center;
    margin-bottom: 20px;
}

.payment-instruction p {
    color: #7d8590;
    font-size: 16px;
    margin: 0;
}

.qr-section {
    text-align: center;
    margin-bottom: 20px;
}

.qr-code {
    width: 200px;
    height: 200px;
    background: white;
    border-radius: 12px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.qr-placeholder {
    color: #666;
    font-size: 14px;
}

.qr-timer {
    color: #7d8590;
    font-size: 14px;
    margin-bottom: 20px;
}

.payment-notice {
    text-align: center;
    margin-bottom: 30px;
}

.payment-notice p {
    color: #7d8590;
    font-size: 14px;
    margin: 0;
}

.payment-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.payment-actions .btn {
    width: 100%;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-actions .btn-primary {
    background: #6366f1;
    color: white;
}

.payment-actions .btn-primary:hover {
    background: #5b21b6;
}

.payment-actions .btn-secondary {
    background: #30363d;
    color: #7d8590;
    border: 1px solid #484f58;
}

.payment-actions .btn-secondary:hover {
    background: #484f58;
    color: #f0f6fc;
}


