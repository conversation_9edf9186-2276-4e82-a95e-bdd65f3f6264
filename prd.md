# VS Code 插件产品需求文档 (PRD)

## 1. 产品概述
开发一款VS Code插件，为用户生成一个邮箱、该邮箱可以拿去注册任意网站，注册后的邮箱验证码 要及时的显示在插件主页中，方便用户复制。

## 2. 核心功能

### 2.1 用户认证模块
- **邮箱注册**：用户输入邮箱地址进行注册
- **用户登录**：已注册用户邮箱登录
- **获取邮箱按钮**：一键获取当前登录用户邮箱信息
- **验证码接收区域**：显示和输入邮箱验证码的界面

### 2.2 系统管理模块
- **清理环境按钮**：重启宿主VS Code以清理插件环境
- **公告区域**：显示系统公告和重要通知

### 2.3 会员服务模块
- **用户类型显示**：区分并显示VIP用户和普通用户
- **套餐显示**：展示当前用户的套餐信息和权益
- **二维码显示**：用于支付或其他功能的二维码展示

## 3. 技术规格

### 3.1 开发环境
- **平台**：VS Code Extension
- **开发语言**：TypeScript/JavaScript
- **框架**：VS Code Extension API

### 3.2 UI/UX 设计
- **界面风格**：遵循VS Code原生设计语言
- **布局方式**：侧边栏面板或状态栏集成
- **响应式设计**：适配不同屏幕尺寸

## 4. 功能详细说明

### 4.1 邮箱注册流程
1. 用户点击注册按钮
2. 输入邮箱地址，密码，重复密码
3. 系统发送验证码到邮箱
4. 用户在验证码区域输入验证码
5. 验证成功后完成注册
6. 完成注册 后 应该会直接登陆，，因为注册成功后会返回token，
7. 登陆状态要变化

### 4.2 登录流程
1. 用户输入已注册邮箱  和密码进行 登录
2. 显示用户信息和权限状态
3. 个人中心页面 是单独的页面，需要 点击 主页的按钮进入，主页不需要显示众多的内容

### 4.3 会员管理
- **普通用户**：基础功能权限
- **VIP用户**：高级功能权限
- **套餐升级**：通过二维码支付升级


## 6. 性能要求
- 插件启动时间 < 2秒
- 邮箱验证响应时间 < 5秒
- 界面操作响应时间 < 1秒
- 内存占用 < 50MB

## 7. 兼容性
- VS Code版本：1.60.0及以上
- 操作系统：Windows、macOS、Linux
- 网络环境：支持代理和防火墙环境

## 8. 数据存储
- 用户登录状态：本地存储
- 用户配置：VS Code设置同步
- 临时数据：内存存储，重启清理

## 9. 错误处理
- 网络连接失败提示
- 验证码错误重试机制
- 邮箱格式验证
- 服务器异常友好提示

## 10. 主页展示内容
- 重置插件按钮
- 获取邮箱按钮
- 显示从API获取到的邮箱，旁边有复制按钮
- 显示从API拿到的邮箱验证码，旁边有复制按钮
- 显示从API拿到的邮箱验证码，旁边有复制按钮，旁边也有一个按钮，点击可以重新获取验证码
- 显示公告的区域，公告内容从API获取，可换行

