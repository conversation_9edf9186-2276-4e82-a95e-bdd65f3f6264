# 插件重置功能优化总结

## 修改概述

根据用户需求，修改了插件的重置功能，使其在重置完成后不再重启宿主，而是直接查找并关闭所有宿主进程。

## 主要修改内容

### 1. 新增方法

#### `handleKillHostProcesses()` - 处理关闭所有宿主进程
- 查找所有宿主进程
- 显示确认对话框，列出将要关闭的进程
- 显示进度并逐个关闭进程
- 提供详细的用户反馈

#### `findAllHostProcesses()` - 查找所有宿主进程
- 跨平台支持（macOS/Linux/Windows）
- 进程分类（主进程、渲染进程、插件进程、GPU进程等）
- 按正确顺序排序（先关闭子进程，最后关闭主进程）
- 返回进程信息数组

#### `killProcessesByPids()` - 按PID关闭进程
- 支持温和关闭（TERM信号）和强制关闭（KILL信号）
- 跨平台命令支持
- 进度报告
- 错误处理，单个进程失败不影响整体流程

### 2. 修改现有功能

#### `handleResetPlugin()` - 重置插件逻辑修改
- 重置完成后提供两个选项：
  - "关闭所有进程"（新功能）
  - "仅重启当前窗口"（原有功能）
- 默认推荐关闭所有进程

#### 消息处理逻辑
- 在 `TempMailPanel` 中添加 `killHostProcesses` 命令处理
- 在 `SidebarProvider` 中添加相应的消息处理
- 将 `handleKillHostProcesses` 方法改为公有方法

#### 前端逻辑修改
- 修改 `resetPlugin()` 函数
- 移除自动重启宿主的逻辑
- 改为由后端自动处理进程关闭

### 3. 进程关闭策略

#### 进程检测
```bash
# macOS/Linux
ps aux | grep -E "Visual Studio Code|Code Helper|Electron" | grep -v grep

# Windows
tasklist | findstr /I "Code.exe Electron.exe"
```

#### 进程分类和关闭顺序
1. Plugin 进程（插件进程）
2. Renderer 进程（渲染进程）
3. Helper 进程（辅助进程）
4. GPU 进程（GPU进程）
5. Main 进程（主进程）
6. Unknown 进程（未知进程）

#### 关闭命令
```bash
# macOS/Linux - 先温和关闭，失败则强制关闭
kill -TERM <PID> || kill -9 <PID>

# Windows - 强制关闭
taskkill /PID <PID> /F
```

## 用户体验改进

### 1. 确认对话框
- 显示将要关闭的进程列表
- 明确警告未保存工作将丢失
- 提供取消选项

### 2. 进度显示
- 实时显示关闭进程的进度
- 显示当前正在关闭的进程类型和PID
- 完成后显示成功消息

### 3. 错误处理
- 单个进程关闭失败不影响整体流程
- 详细的错误日志输出
- 用户友好的错误提示

### 4. 选择灵活性
- 重置后可选择关闭所有进程或仅重启当前窗口
- 保留原有的重启功能作为备选

## 安全性考虑

### 1. 进程识别
- 只关闭VS Code相关进程
- 避免误杀其他系统进程
- 进程名称和路径双重验证

### 2. 权限处理
- 使用现有的权限处理机制
- 支持管理员权限提升
- 跨平台权限兼容

### 3. 用户确认
- 多重确认机制
- 明确的警告信息
- 可取消操作

## 测试验证

创建了 `test-kill-processes.js` 测试脚本：
- 验证进程检测逻辑
- 验证进程分类和排序
- 验证关闭命令生成
- 不实际执行关闭操作，确保安全

## 编译状态

✅ 编译成功，无语法错误
✅ 类型检查通过
✅ ESLint 检查通过
✅ 打包成功

## 使用方法

1. 用户在插件中点击"重置插件"
2. 插件执行重置操作
3. 重置完成后，用户可选择：
   - "关闭所有进程"：查找并关闭所有VS Code进程
   - "仅重启当前窗口"：传统的重启方式
4. 如果选择关闭所有进程，系统会：
   - 显示将要关闭的进程列表
   - 要求用户确认
   - 按顺序关闭所有进程
   - 显示进度和结果

## 注意事项

1. 关闭所有进程后，用户需要手动重新启动VS Code
2. 所有未保存的工作将丢失
3. 建议在执行前保存所有重要工作
4. 该功能需要VIP权限才能使用

## 总结

此次修改成功实现了用户的需求：
- ✅ 重置插件后不自动重启宿主
- ✅ 提供查找并关闭所有宿主进程的功能
- ✅ 保持良好的用户体验和安全性
- ✅ 跨平台兼容性
- ✅ 详细的进度反馈和错误处理
