{"openapi": "3.1.0", "info": {"title": "Cursor Star接口文档", "description": "提供Cursor应用的所有API接口", "contact": {"name": "管理员", "url": "https://www.examapp.cn", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "v1.0.0"}, "servers": [{"url": "http://localhost:8080/cursor-api", "description": "Generated server url"}], "tags": [{"name": "脚本管理", "description": "客户端脚本获取和变量设置相关接口"}, {"name": "常见问题", "description": "常见问题(FAQ)的增删改查接口"}, {"name": "用户认证", "description": "包含用户注册、登录、验证码发送、密码重置等接口"}, {"name": "邮箱池管理", "description": "VIP用户邮箱池相关接口"}, {"name": "系统信息", "description": "联系方式、软件介绍等系统信息相关接口"}, {"name": "版本管理", "description": "客户端版本管理相关接口"}, {"name": "订单支付", "description": "订单创建、查询、支付、取消等接口"}, {"name": "邮箱黑名单", "description": "邮箱后缀黑名单管理接口"}], "paths": {"/api/faqs/{id}": {"get": {"tags": ["常见问题"], "summary": "获取FAQ详情", "description": "获取指定ID的FAQ详细信息", "operationId": "getFaq", "parameters": [{"name": "id", "in": "path", "description": "FAQ ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqResponse"}}}}, "404": {"description": "FAQ记录不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseFaqResponse"}}}}}}, "put": {"tags": ["常见问题"], "summary": "更新FAQ", "description": "更新现有FAQ记录，需要管理员权限", "operationId": "updateFaq", "parameters": [{"name": "id", "in": "path", "description": "FAQ ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqResponse"}}}}, "404": {"description": "FAQ记录不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseFaqResponse"}}}}}}, "delete": {"tags": ["常见问题"], "summary": "删除FAQ", "description": "删除指定ID的FAQ记录，需要管理员权限", "operationId": "deleteFaq", "parameters": [{"name": "id", "in": "path", "description": "FAQ ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "404": {"description": "FAQ记录不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/email-blacklist/status/{id}": {"put": {"tags": ["邮箱黑名单"], "summary": "更新黑名单状态", "description": "启用或禁用黑名单邮箱后缀", "operationId": "updateStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/tokens/batch": {"post": {"tags": ["token-controller"], "operationId": "batchSaveOrUpdateTokens", "parameters": [{"name": "myAuthCode", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTokenRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}}}}, "/api/payment/qrcode/{orderNo}": {"post": {"tags": ["payment-controller"], "operationId": "refreshPaymentQrCode", "parameters": [{"name": "orderNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringString"}}}}}}}, "/api/payment/callback": {"post": {"tags": ["payment-controller"], "operationId": "handleCallback", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentCallbackDTO"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/payment/callback/alipay": {"post": {"tags": ["payment-callback-controller"], "operationId": "handleAlipayCallback", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/orders": {"post": {"tags": ["订单支付"], "summary": "创建订单", "description": "创建会员购买或升级订单", "operationId": "createOrder", "requestBody": {"description": "创建订单请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}}}}, "/api/orders/{orderNo}/cancel": {"post": {"tags": ["订单支付"], "summary": "取消订单", "description": "取消未支付的订单", "operationId": "cancelOrder", "parameters": [{"name": "orderNo", "in": "path", "description": "订单号", "required": true, "schema": {"type": "string"}, "example": "ORD20231215001234"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "不允许取消（已支付或已过期）", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "取消成功", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "404": {"description": "订单不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/notify/alipay": {"post": {"tags": ["payment-notify-controller"], "operationId": "handleAlipayNotify", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/faqs": {"get": {"tags": ["常见问题"], "summary": "获取公开FAQ列表", "description": "获取所有已发布的FAQ列表，按照排序顺序降序排列", "operationId": "listPublicFaqs", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FaqResponse"}}}}}}}, "post": {"tags": ["常见问题"], "summary": "创建FAQ", "description": "创建新的FAQ记录，需要管理员权限", "operationId": "createFaq", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqResponse"}}}}}}}, "/api/email-pool/start-check": {"post": {"tags": ["邮箱池管理"], "summary": "开始检测验证码", "description": "用户点击已发送后，开始检测邮箱中的验证码", "operationId": "startVerificationCheck", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationCheckRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "开始检测成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "12104": {"description": "邮箱未分配给当前用户", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}}}}, "/api/email-pool/request": {"post": {"tags": ["邮箱池管理"], "summary": "申请邮箱", "description": "VIP用户申请一个可用的163邮箱", "operationId": "requestEmail", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "申请成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequestResponse"}}}}, "12100": {"description": "仅VIP用户可使用", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseEmailRequestResponse"}}}}, "12101": {"description": "邮箱池已耗尽", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseEmailRequestResponse"}}}}, "12102": {"description": "今日申请次数已达上限", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseEmailRequestResponse"}}}}}}}, "/api/email-pool/batch": {"post": {"tags": ["邮箱池管理"], "summary": "批量导入邮箱", "description": "管理员批量导入163邮箱到邮箱池", "operationId": "batchSaveEmails", "parameters": [{"name": "myAuthCode", "in": "query", "description": "授权码", "required": true, "schema": {"type": "string"}, "example": "yunzhongauth1996"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchEmailRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "导入成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}, "10000": {"description": "授权码错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}}}}, "/api/email-blacklist/add": {"post": {"tags": ["邮箱黑名单"], "summary": "添加邮箱到黑名单", "description": "添加一个邮箱到黑名单，系统自动提取后缀", "operationId": "addEmailToBlacklist", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "description", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/daily-boost/purchase": {"post": {"tags": ["daily-boost-controller"], "operationId": "createDailyBoostOrder", "parameters": [{"name": "vipPackageId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "paymentMethod", "in": "query", "required": false, "schema": {"type": "string", "default": "alipay"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseOrderResponse"}}}}}}}, "/api/auth/send-reset-password-code": {"post": {"tags": ["用户认证"], "summary": "发送重置密码验证码", "description": "发送邮箱验证码用于重置密码", "operationId": "sendResetPasswordCode", "parameters": [{"name": "email", "in": "query", "description": "接收验证码的邮箱地址", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}], "responses": {"400": {"description": "邮箱格式错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "验证码发送成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}, "429": {"description": "操作频繁，请稍后再试", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/auth/send-code": {"post": {"tags": ["用户认证"], "summary": "发送注册验证码", "description": "发送邮箱验证码用于新用户注册", "operationId": "sendVerificationCode", "parameters": [{"name": "email", "in": "query", "description": "接收验证码的邮箱地址", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}], "responses": {"400": {"description": "邮箱格式错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "验证码发送成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}, "429": {"description": "操作频繁，请稍后再试", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/auth/reset-password": {"post": {"tags": ["用户认证"], "summary": "重置密码", "description": "通过验证码重置用户密码", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误或验证码无效", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "密码重置成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtResponse"}}}}, "404": {"description": "用户不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseJwtResponse"}}}}}}}, "/api/auth/register": {"post": {"tags": ["用户认证"], "summary": "用户注册", "description": "新用户注册接口，需要提供邮箱、密码和验证码", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误或验证码无效", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "注册成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtResponse"}}}}, "409": {"description": "邮箱已被注册", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseJwtResponse"}}}}}}}, "/api/auth/refresh-token": {"post": {"tags": ["用户认证"], "summary": "刷新令牌", "description": "使用刷新令牌获取新的访问令牌", "operationId": "refreshToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "刷新令牌无效或已过期", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "刷新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtResponse"}}}}}}}, "/api/auth/logout": {"post": {"tags": ["用户认证"], "summary": "用户登出", "description": "使当前用户的令牌失效", "operationId": "logout", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未登录或登录已过期", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "登出成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/auth/login": {"post": {"tags": ["用户认证"], "summary": "用户登录", "description": "用户登录接口，使用邮箱和密码进行认证", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "邮箱或密码错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtResponse"}}}}}}}, "/api/vip/status": {"get": {"tags": ["vip-controller"], "operationId": "getVipStatus", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringObject"}}}}}}}, "/api/vip/packages": {"get": {"tags": ["vip-package-controller"], "operationId": "listPackages", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListVipPackage"}}}}}}}, "/api/vip/packages/{id}": {"get": {"tags": ["vip-package-controller"], "operationId": "getPackage", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVipPackage"}}}}}}}, "/api/vip/packages/type/{type}": {"get": {"tags": ["vip-package-controller"], "operationId": "getPackageByType", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVipPackage"}}}}}}}, "/api/version/check": {"get": {"tags": ["版本管理"], "summary": "检查版本更新", "description": "客户端提交当前版本，返回版本更新信息", "operationId": "checkVersion", "parameters": [{"name": "currentVersion", "in": "query", "description": "当前客户端版本号", "required": true, "schema": {"type": "string"}, "example": "1.0.0"}], "responses": {"400": {"description": "参数错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionCheckResponse"}}}}}}}, "/api/tokens/unused": {"get": {"tags": ["token-controller"], "operationId": "getUnusedToken", "parameters": [{"name": "userId", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseObfuscatedTokenResponse"}}}}}}}, "/api/tokens/filter-and-increment": {"get": {"tags": ["token-controller"], "operationId": "getTokenByConditionAndIncrementDeleteCount", "parameters": [{"name": "myAuthCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "deleteCount", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "registrationCount", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseTokenResponse"}}}}}}}, "/api/stats/monthly": {"get": {"tags": ["user-usage-stats-controller"], "summary": "获取月度使用统计", "description": "获取当前登录用户指定月份的使用统计", "operationId": "getMonthlyUsage", "parameters": [{"name": "year", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}}}}, "/api/stats/monthly-detail": {"get": {"tags": ["user-usage-stats-controller"], "summary": "获取月度详细使用统计", "description": "获取当前登录用户指定月份的详细使用统计", "operationId": "getMonthlyDetailUsage", "parameters": [{"name": "year", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringInteger"}}}}}}}, "/api/stats/daily": {"get": {"tags": ["user-usage-stats-controller"], "summary": "获取每日使用统计", "description": "获取当前登录用户指定日期的使用统计", "operationId": "getDailyUsage", "parameters": [{"name": "year", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "day", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringInteger"}}}}}}}, "/api/scripts/{scriptName}": {"get": {"tags": ["脚本管理"], "summary": "获取脚本", "description": "获取指定名称的脚本内容，支持根据操作系统类型区分脚本。脚本内容会进行变量替换：{{USER_ID}}, {{USER_EMAIL}}, {{CLIENT_ID}}, {{TIMESTAMP}}", "operationId": "getScript", "parameters": [{"name": "scriptName", "in": "path", "description": "脚本名称", "required": true, "schema": {"type": "string"}, "example": "setup.sh"}, {"name": "osType", "in": "query", "description": "操作系统类型", "required": false, "schema": {"type": "string", "enum": ["linux", "mac", "windows"]}, "example": "linux"}, {"name": "appVersion", "in": "query", "description": "客户端版本号", "required": false, "schema": {"type": "string"}, "example": "1.0.0"}, {"name": "clientId", "in": "query", "description": "客户端ID", "required": false, "schema": {"type": "integer", "format": "int64"}, "example": 1001}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"text/plain": {"schema": {"type": "string", "example": "echo Hello User {{USER_ID}}"}}}}, "404": {"description": "脚本不存在", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/api/scripts/init": {"get": {"tags": ["脚本管理"], "summary": "获取初始化脚本", "description": "获取用于初始化客户端环境的脚本内容。脚本内容会进行变量替换：{{USER_ID}}, {{USER_EMAIL}}, {{CLIENT_ID}}, {{TIMESTAMP}}", "operationId": "getInitScript", "parameters": [{"name": "osType", "in": "query", "description": "操作系统类型", "required": false, "schema": {"type": "string", "enum": ["linux", "mac", "windows"]}, "example": "linux"}, {"name": "appVersion", "in": "query", "description": "客户端版本号", "required": false, "schema": {"type": "string"}, "example": "1.0.0"}, {"name": "clientId", "in": "query", "description": "客户端ID", "required": false, "schema": {"type": "integer", "format": "int64"}, "example": 1001}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"text/plain": {"schema": {"type": "string", "example": "echo Initializing... User: {{USER_EMAIL}}"}}}}, "404": {"description": "脚本不存在", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/api/payment/ws-token": {"get": {"tags": ["payment-controller"], "operationId": "getWebSocketToken", "parameters": [{"name": "orderNo", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringObject"}}}}}}}, "/api/payment/status/{orderNo}": {"get": {"tags": ["payment-controller"], "operationId": "getPaymentStatus", "parameters": [{"name": "orderNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringObject"}}}}}}}, "/api/orders/{orderNo}": {"get": {"tags": ["订单支付"], "summary": "查询订单", "description": "根据订单号查询订单详情", "operationId": "getOrder", "parameters": [{"name": "orderNo", "in": "path", "description": "订单号", "required": true, "schema": {"type": "string"}, "example": "ORD20231215001234"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "404": {"description": "订单不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseOrderResponse"}}}}}}}, "/api/faqs/url": {"get": {"tags": ["常见问题"], "summary": "获取FAQ网页链接", "description": "获取配置在yaml中的FAQ网页链接", "operationId": "getFaqUrl", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/faqs/all": {"get": {"tags": ["常见问题"], "summary": "获取所有FAQ列表(管理员)", "description": "获取所有FAQ列表（包括未发布的），需要管理员权限", "operationId": "listAllFaqs", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未认证", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FaqResponse"}}}}}}}}, "/api/email-pool/status/{emailAddress}": {"get": {"tags": ["邮箱池管理"], "summary": "查询邮箱状态", "description": "客户端轮询接口，获取邮箱检测状态和验证码", "operationId": "getEmailStatus", "parameters": [{"name": "emailAddress", "in": "path", "description": "邮箱地址", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailStatusResponse"}}}}, "12104": {"description": "邮箱未分配给当前用户", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseEmailStatusResponse"}}}}}}}, "/api/email-blacklist/list": {"get": {"tags": ["邮箱黑名单"], "summary": "获取所有黑名单后缀", "description": "获取所有黑名单邮箱后缀（不分页）", "operationId": "listBlacklistedSuffixes", "parameters": [{"name": "keyword", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/email-blacklist/enabled": {"get": {"tags": ["邮箱黑名单"], "summary": "获取所有启用的黑名单后缀", "description": "获取所有当前启用状态的黑名单邮箱后缀", "operationId": "getEnabledBlacklistedSuffixes", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlacklistedEmailSuffix"}}}}}}}}, "/api/email-blacklist/check": {"get": {"tags": ["邮箱黑名单"], "summary": "检查邮箱是否被拉黑", "description": "检查指定邮箱是否在黑名单中", "operationId": "checkEmail", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/daily-boost/today-boost": {"get": {"tags": ["daily-boost-controller"], "operationId": "getTodayBoostCount", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}}}}, "/api/daily-boost/can-purchase": {"get": {"tags": ["daily-boost-controller"], "operationId": "canPurchase", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/contact": {"get": {"tags": ["系统信息"], "summary": "获取联系方式", "description": "获取系统配置的联系方式信息", "operationId": "getContactInfo", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactResponse"}}}}}}}, "/api/auth/status": {"get": {"tags": ["用户认证"], "summary": "获取用户状态", "description": "获取当前登录用户的状态信息", "operationId": "getUserStatus", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "未登录或登录已过期", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserStatusResponse"}}}}}}}, "/api/auth/check-email": {"get": {"tags": ["用户认证"], "summary": "检查邮箱是否已注册", "description": "检查指定邮箱是否已被注册", "operationId": "checkEmail_1", "parameters": [{"name": "email", "in": "query", "description": "待检查的邮箱地址", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}], "responses": {"400": {"description": "邮箱格式错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "检查成功", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/auth/check-email-domain": {"get": {"tags": ["用户认证"], "summary": "检查邮箱域名是否允许", "description": "检查指定邮箱是否允许注册", "operationId": "checkEmailDomain", "parameters": [{"name": "email", "in": "query", "description": "待检查的邮箱地址", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}], "responses": {"400": {"description": "邮箱格式错误", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "检查成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/allowed-email-domains": {"get": {"tags": ["用户认证"], "summary": "获取允许的邮箱域名列表", "description": "获取系统允许注册的邮箱域名列表", "operationId": "getAllowedEmailDomains", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/app-info": {"get": {"tags": ["系统信息"], "summary": "获取应用信息", "description": "获取系统配置的应用基本信息", "operationId": "getAppInfo", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppInfoResponse"}}}}}}}, "/api/email-blacklist/{id}": {"delete": {"tags": ["邮箱黑名单"], "summary": "删除黑名单后缀", "description": "根据ID删除一个黑名单邮箱后缀", "operationId": "removeSuffixFromBlacklist", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/ApiResponseString"}, {"$ref": "#/components/schemas/ApiResponseMapStringString"}]}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseString"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "components": {"schemas": {"ApiResponseMapStringString": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "additionalProperties": {"type": "string"}, "description": "响应数据"}}, "required": ["code"]}, "ApiResponseString": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "string", "description": "响应数据"}}, "required": ["code"]}, "FaqRequest": {"type": "object", "description": "FAQ创建请求", "properties": {"question": {"type": "string", "description": "问题内容", "example": "如何升级为VIP用户?", "maxLength": 255, "minLength": 0}, "answer": {"type": "string", "description": "答案内容", "example": "您可以在个人中心点击升级VIP按钮...", "maxLength": 4000, "minLength": 0}, "sortOrder": {"type": "integer", "format": "int32", "default": "0", "description": "排序顺序(数字越小排序越靠前)", "example": 0}, "published": {"type": "boolean", "default": "false", "description": "是否发布", "example": false}}, "required": ["answer", "published", "question", "sortOrder"]}, "FaqResponse": {"type": "object", "description": "FAQ响应对象", "properties": {"id": {"type": "integer", "format": "int64", "description": "FAQ ID", "example": 1}, "question": {"type": "string", "description": "问题内容", "example": "如何升级为VIP用户?"}, "answer": {"type": "string", "description": "答案内容", "example": "您可以在个人中心点击升级VIP按钮..."}, "sortOrder": {"type": "integer", "format": "int32", "description": "排序顺序(数字越小排序越靠前)", "example": 0}, "isPublished": {"type": "integer", "format": "int32", "description": "是否发布 (1:是, 0:否)", "enum": ["0", "1"], "example": 1}, "createdTime": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2023-12-15T10:30:00"}, "updatedTime": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2023-12-15T14:45:00"}}}, "ApiResponseFaqResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/FaqResponse", "description": "响应数据"}}, "required": ["code"]}, "AccountTokenRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "cachedEmail": {"type": "string"}, "accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "cookies": {"type": "string"}, "machineId": {"type": "string"}, "telemetryMachineId": {"type": "string"}, "envMachineId": {"type": "string"}, "anonymizedId": {"type": "string"}, "sessionId": {"type": "string"}, "agentId": {"type": "string"}, "telemetryAnonId": {"type": "string"}, "telemetryMachineIdHash": {"type": "string"}, "telemetrySessionId": {"type": "string"}, "macMachineId": {"type": "string"}, "installationId": {"type": "string"}, "storageServiceMachineId": {"type": "string"}, "telemetrySqmId": {"type": "string"}, "telemetryDevDeviceId": {"type": "string"}, "registrationCount": {"type": "integer", "format": "int32"}, "deleteCount": {"type": "integer", "format": "int32"}, "isUsed": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "BatchTokenRequest": {"type": "object", "properties": {"tokens": {"type": "array", "items": {"$ref": "#/components/schemas/AccountTokenRequest"}}}}, "ApiResponseInteger": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "integer", "format": "int32", "description": "响应数据"}}, "required": ["code"]}, "PaymentCallbackDTO": {"type": "object", "properties": {"orderId": {"type": "string"}, "outTradeNo": {"type": "string"}, "money": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "payTime": {"type": "string"}, "sign": {"type": "string"}, "attach": {"type": "string"}}}, "CreateOrderRequest": {"type": "object", "description": "创建订单请求", "properties": {"membershipTypeId": {"type": "integer", "format": "int64", "description": "会员类型ID", "example": 1}, "paymentMethod": {"type": "string", "description": "支付方式", "enum": ["alipay", "wechat"], "example": "alipay", "minLength": 1}, "couponCode": {"type": "string", "description": "优惠券代码", "example": "DISCOUNT2024"}}, "required": ["membershipTypeId", "paymentMethod"]}, "OrderResponse": {"type": "object", "description": "订单响应对象", "properties": {"id": {"type": "integer", "format": "int64", "description": "订单ID", "example": 1001}, "orderNo": {"type": "string", "description": "订单号", "example": "ORD20231215001234"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 50}, "amount": {"type": "number", "description": "订单金额", "example": 99.0}, "title": {"type": "string", "description": "订单标题", "example": "VIP会员年费套餐"}, "description": {"type": "string", "description": "订单描述", "example": "购买VIP会员年费套餐，享受一年高级服务"}, "payType": {"type": "string", "description": "支付方式", "enum": ["alipay", "wechat"], "example": "alipay"}, "status": {"type": "string", "description": "订单状态", "enum": ["PENDING", "PAID", "EXPIRED", "CANCELED"], "example": "PENDING"}, "vipType": {"type": "integer", "format": "int32", "description": "VIP套餐类型(关联字典或枚举)", "example": 1}, "vipDuration": {"type": "integer", "format": "int32", "description": "VIP有效期(天数)", "example": 365}, "qrcodeUrl": {"type": "string", "description": "支付二维码URL(部分支付方式提供)", "example": "https://qr.alipay.com/xxx"}, "qrcodeContent": {"type": "string", "description": "支付二维码内容(部分支付方式提供)", "example": "wxp://f2f0oX...", "readOnly": true}, "paidTime": {"type": "string", "format": "date-time", "description": "支付时间", "example": "2023-12-15T10:30:00"}, "expireTime": {"type": "string", "format": "date-time", "description": "订单过期时间", "example": "2023-12-15T10:45:00"}, "createdTime": {"type": "string", "format": "date-time", "description": "订单创建时间", "example": "2023-12-15T10:15:00"}}}, "ApiResponseBoolean": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "boolean", "description": "响应数据"}}, "required": ["code"]}, "VerificationCheckRequest": {"type": "object", "description": "验证码检测请求", "properties": {"emailAddress": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>", "minLength": 1}}, "required": ["emailAddress"]}, "EmailRequestResponse": {"type": "object", "description": "邮箱申请响应", "properties": {"emailAddress": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>"}, "expiresIn": {"type": "integer", "format": "int64", "description": "过期时间（秒）", "example": 345600}, "dailyRemaining": {"type": "integer", "format": "int32", "description": "今日剩余申请次数", "example": 4}}}, "ApiResponseEmailRequestResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/EmailRequestResponse", "description": "响应数据"}}, "required": ["code"]}, "BatchEmailRequest": {"type": "object", "description": "批量邮箱导入请求", "properties": {"emails": {"type": "array", "description": "邮箱地址列表", "example": ["<EMAIL>", "<EMAIL>"], "items": {"type": "string"}, "maxItems": 1000, "minItems": 0}}, "required": ["emails"]}, "ApiResponseOrderResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/OrderResponse", "description": "响应数据"}}, "required": ["code"]}, "ResetPasswordRequest": {"type": "object", "description": "重置密码请求信息", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>", "minLength": 1}, "verificationCode": {"type": "string", "description": "邮箱验证码", "example": 123456, "maxLength": 6, "minLength": 1}, "newPassword": {"type": "string", "description": "新密码", "example": "NewPassword456", "maxLength": 2147483647, "minLength": 6}}, "required": ["email", "newPassword", "verificationCode"]}, "JwtResponse": {"type": "object", "description": "JWT认证响应，包含令牌和用户信息", "properties": {"accessToken": {"type": "string", "description": "访问令牌 (Access Token)", "example": "eyJhbGciOiJIUzUxMiJ9..."}, "refreshToken": {"type": "string", "description": "刷新令牌 (Refresh <PERSON>)", "example": "eyJhbGciOiJIUzUxMiJ9..."}, "userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 1001}, "email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}, "role": {"type": "string", "description": "用户角色", "example": "FREE"}, "username": {"type": "string", "description": "用户名 (可能与邮箱相同或自定义)", "example": "<EMAIL>"}}}, "ApiResponseJwtResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/JwtResponse", "description": "响应数据"}}, "required": ["code"]}, "RegisterRequest": {"type": "object", "description": "注册信息", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>", "minLength": 1}, "password": {"type": "string", "description": "用户密码", "example": "Password123", "maxLength": 2147483647, "minLength": 6}, "verificationCode": {"type": "string", "description": "邮箱验证码", "example": 123456, "maxLength": 6, "minLength": 1}}, "required": ["email", "password", "verificationCode"]}, "RefreshTokenRequest": {"type": "object", "description": "刷新令牌请求", "properties": {"refreshToken": {"type": "string", "description": "刷新令牌", "example": "eyJhbGciOiJIUzUxMiJ9...", "minLength": 1}}}, "ApiResponseVoid": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "description": "响应数据"}}, "required": ["code"]}, "LoginRequest": {"type": "object", "description": "登录信息", "properties": {"email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>", "minLength": 1}, "password": {"type": "string", "description": "用户密码", "example": "Password123", "minLength": 1}}, "required": ["email", "password"]}, "ApiResponseMapStringObject": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "additionalProperties": {"type": "object"}, "description": "响应数据"}}, "required": ["code"]}, "ApiResponseListVipPackage": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/VipPackage"}}}, "required": ["code"]}, "VipPackage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "duration": {"type": "integer", "format": "int32"}, "price": {"type": "number"}, "originalPrice": {"type": "number"}, "description": {"type": "string"}, "tag": {"type": "string"}, "maxDailyCount": {"type": "integer", "format": "int32"}, "benefitsDesc": {"type": "string"}, "recommendLevel": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "sortOrder": {"type": "integer", "format": "int32"}, "createdTime": {"type": "string", "format": "date-time"}, "updatedTime": {"type": "string", "format": "date-time"}}}, "ApiResponseVipPackage": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/VipPackage", "description": "响应数据"}}, "required": ["code"]}, "VersionCheckResponse": {"type": "object", "description": "版本检查响应", "properties": {"updateAvailable": {"type": "boolean", "description": "是否有可用更新", "example": true}, "forceUpdate": {"type": "boolean", "description": "是否需要强制更新", "example": false}, "latestVersion": {"type": "string", "description": "最新版本号", "example": "1.2.0"}, "downloadUrl": {"type": "string", "description": "新版本下载地址", "example": "https://example.com/download/v1.2.0/app.exe"}, "releaseNotes": {"type": "string", "description": "版本更新说明", "example": "- 修复了xxx bug\n- 优化了yyy性能"}}}, "ApiResponseObfuscatedTokenResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/ObfuscatedTokenResponse", "description": "响应数据"}}, "required": ["code"]}, "ObfuscatedTokenResponse": {"type": "object", "properties": {"p1": {"type": "integer", "format": "int64"}, "p2": {"type": "string"}, "p3": {"type": "string"}, "p4": {"type": "string"}, "p5": {"type": "string"}, "p6": {"type": "string"}, "p7": {"type": "string"}, "p8": {"type": "string"}, "p9": {"type": "string"}, "p10": {"type": "string"}, "p11": {"type": "string"}, "p12": {"type": "string"}, "p13": {"type": "string"}, "p14": {"type": "string"}, "p15": {"type": "string"}, "p16": {"type": "string"}, "p17": {"type": "string"}, "p18": {"type": "string"}, "p19": {"type": "string"}}}, "ApiResponseTokenResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/TokenResponse", "description": "响应数据"}}, "required": ["code"]}, "TokenResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "cachedEmail": {"type": "string"}, "accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "cookies": {"type": "string"}, "machineId": {"type": "string"}, "telemetryMachineId": {"type": "string"}, "envMachineId": {"type": "string"}, "anonymizedId": {"type": "string"}, "sessionId": {"type": "string"}, "agentId": {"type": "string"}, "telemetryAnonId": {"type": "string"}, "telemetryMachineIdHash": {"type": "string"}, "telemetrySessionId": {"type": "string"}, "macMachineId": {"type": "string"}, "installationId": {"type": "string"}, "storageServiceMachineId": {"type": "string"}, "telemetrySqmId": {"type": "string"}, "telemetryDevDeviceId": {"type": "string"}, "registrationCount": {"type": "integer", "format": "int32"}, "deleteCount": {"type": "integer", "format": "int32"}, "isUsed": {"type": "boolean"}}}, "ApiResponseMapStringInteger": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "响应数据"}}, "required": ["code"]}, "EmailStatusResponse": {"type": "object", "description": "邮箱状态响应", "properties": {"emailAddress": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>"}, "status": {"type": "string", "description": "邮箱状态", "example": "assigned"}, "checkStatus": {"type": "string", "description": "检测状态", "example": "checking"}, "verificationCode": {"type": "string", "description": "验证码", "example": 123456}, "assignedAt": {"type": "string", "format": "date-time", "description": "分配时间", "example": "2024-01-01T10:00:00"}, "checkStartedAt": {"type": "string", "format": "date-time", "description": "检测开始时间", "example": "2024-01-01T10:05:00"}, "expiresAt": {"type": "string", "format": "date-time", "description": "过期时间", "example": "2024-01-05T10:00:00"}}}, "ApiResponseEmailStatusResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/EmailStatusResponse", "description": "响应数据"}}, "required": ["code"]}, "BlacklistedEmailSuffix": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "suffix": {"type": "string"}, "description": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}}}, "ContactItem": {"type": "object", "description": "单个联系方式项目", "properties": {"type": {"type": "string", "description": "联系方式类型", "enum": ["email", "phone", "wechat", "qq", "website", "address"], "example": "email"}, "value": {"type": "string", "description": "联系方式的值", "example": "<EMAIL>"}, "description": {"type": "string", "description": "描述信息", "example": "技术支持邮箱"}, "qrCodeUrl": {"type": "string", "description": "二维码图片URL（可选）", "example": "https://example.com/qrcode.png"}}}, "ContactResponse": {"type": "object", "description": "联系方式响应", "properties": {"contacts": {"type": "array", "description": "联系方式列表", "items": {"$ref": "#/components/schemas/ContactItem"}}}}, "UserStatusResponse": {"type": "object", "description": "用户状态响应", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 1}, "email": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}, "role": {"type": "string", "description": "用户角色", "example": "FREE"}, "vipLevel": {"type": "integer", "format": "int32", "description": "VIP等级", "example": 0}, "isVip": {"type": "boolean", "description": "是否是VIP用户", "example": false}, "totalCount": {"type": "integer", "format": "int32", "description": "总请求次数", "example": 10}, "maxDailyCount": {"type": "integer", "format": "int32", "description": "每日最大请求次数", "example": 50}, "todayUsedCount": {"type": "integer", "format": "int32", "description": "今日已使用次数", "example": 5}, "vipExpiryDate": {"type": "string", "format": "date-time", "description": "会员到期时间", "example": "2023-12-31T23:59:59"}}}, "ApiResponse": {"type": "object", "description": "通用API响应体", "properties": {"code": {"type": "integer", "format": "int32", "description": "状态码，0表示成功，非0表示失败", "example": 0}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "description": "响应数据"}}, "required": ["code"]}, "AppInfoResponse": {"type": "object", "description": "应用信息响应", "properties": {"appName": {"type": "string", "description": "应用名称", "example": "Cursor Star"}, "mainFeatures": {"type": "array", "description": "主要功能列表", "items": {"$ref": "#/components/schemas/Feature"}}, "usageLimitations": {"$ref": "#/components/schemas/UsageLimitations", "description": "使用限制信息"}}}, "Feature": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}}, "UsageLimitations": {"type": "object", "properties": {"freeUser": {"$ref": "#/components/schemas/UserType"}, "vipUser": {"$ref": "#/components/schemas/UserType"}}}, "UserType": {"type": "object", "properties": {"type": {"type": "string"}, "limitations": {"type": "array", "items": {"type": "string"}}}}}}}