# AugmentStar - 临时邮箱插件

一个功能完整的VSCode临时邮箱插件，提供临时邮箱地址生成和验证码接收功能。

## 功能特色

- 🎯 **一键获取临时邮箱** - 快速生成随机邮箱地址
- 📧 **自动接收验证码** - 模拟真实邮箱验证码接收
- 📋 **一键复制** - 快速复制邮箱地址和验证码
- 🔄 **自动刷新** - 定时检查新验证码
- 🎨 **现代化界面** - GitHub深色主题风格
- 🚀 **流畅体验** - 响应式设计，操作反馈及时

## 安装和使用

### 开发环境运行

1. 克隆项目并安装依赖：
```bash
cd yun
npm install
```

2. 编译插件：
```bash
npm run compile
```

3. 在VSCode中测试：
   - 按 `F5` 启动扩展开发主机
   - 在新窗口中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
   - 输入 "打开临时邮箱" 并选择命令

### 基本操作

1. **获取邮箱**：点击"获取邮箱"按钮生成临时邮箱地址
2. **复制邮箱**：点击邮箱旁边的"复制"按钮
3. **查看验证码**：获取邮箱后会自动接收验证码
4. **复制验证码**：点击验证码旁边的"复制"按钮
5. **刷新验证码**：点击验证码区域的"刷新"按钮
6. **重置插件**：点击"重置插件"按钮清除所有数据

## 技术架构

- **前端框架**：VSCode Extension API
- **UI技术**：HTML5 + CSS3
- **交互逻辑**：JavaScript ES6+
- **开发语言**：TypeScript
- **构建工具**：ESBuild
- **安全策略**：CSP (Content Security Policy)

## 版本历史

### 0.0.1 (当前版本)

- ✅ 基础临时邮箱功能
- ✅ 验证码接收和显示
- ✅ 复制功能
- ✅ 现代化UI界面
- ✅ Toast通知系统
- ✅ 自动刷新机制

**享受使用AugmentStar临时邮箱插件！** 🚀
