# 🎯 AugmentStar 临时邮箱插件演示

## 📸 功能展示

### 主界面
```
┌─────────────────────────────────────────┐
│              Augment Star               │
│                                  [登录] │
├─────────────────────────────────────────┤
│                                         │
│  📧 邮箱地址                            │
│  ┌─────────────────────────────────────┐ │
│  │ 点击获取邮箱按钮生成邮箱地址        │ │
│  │                          [复制(禁用)] │
│  └─────────────────────────────────────┘ │
│  [获取邮箱]              [重置插件]     │
│                                         │
│  🔢 验证码                    [刷新]    │
│  ┌─────────────────────────────────────┐ │
│  │           📭                        │
│  │        暂无验证码                   │
│  └─────────────────────────────────────┘ │
│                                         │
│  📢 系统公告                            │
│  ┌─────────────────────────────────────┐ │
│  │ 🎉 欢迎使用临时邮箱插件！           │
│  │ 📧 支持接收各种网站的验证码邮件     │
│  │ ⭐ VIP用户享有更长的邮箱有效期      │
│  │ 🔄 验证码会自动刷新                │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 获取邮箱后
```
┌─────────────────────────────────────────┐
│              Augment Star               │
│                                  [登录] │
├─────────────────────────────────────────┤
│                                         │
│  📧 邮箱地址                            │
│  ┌─────────────────────────────────────┐ │
│  │ <EMAIL>      [复制] │ │
│  └─────────────────────────────────────┘ │
│  [获取邮箱]              [重置插件]     │
│                                         │
│  🔢 验证码                    [刷新]    │
│  ┌─────────────────────────────────────┐ │
│  │ 123456                       [复制] │ │
│  │ 收到时间: 10:34:08 PM               │
│  └─────────────────────────────────────┘ │
│                                         │
│  📢 系统公告                            │
│  ┌─────────────────────────────────────┐ │
│  │ 🎉 欢迎使用临时邮箱插件！           │
│  │ 📧 支持接收各种网站的验证码邮件     │
│  │ ⭐ VIP用户享有更长的邮箱有效期      │
│  │ 🔄 验证码会自动刷新                │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🎮 操作流程

### 1. 启动插件
- 在VSCode中按 `F5` 启动扩展开发主机
- 在新窗口中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
- 输入 "打开临时邮箱" 并选择命令

### 2. 获取临时邮箱
- 点击 "获取邮箱" 按钮
- 系统显示 "获取邮箱中..." 提示
- 1秒后生成随机邮箱地址
- 复制按钮变为可用状态
- 显示 "邮箱获取成功！" 提示

### 3. 接收验证码
- 获取邮箱3秒后自动接收验证码
- 显示6位数字验证码
- 显示接收时间
- 提供复制按钮

### 4. 复制功能
- 点击邮箱旁的复制按钮复制邮箱地址
- 点击验证码旁的复制按钮复制验证码
- 显示 "已复制到剪贴板" 提示

### 5. 刷新验证码
- 点击验证码区域的 "刷新" 按钮
- 系统显示 "刷新中..." 提示
- 70%概率生成新验证码

### 6. 重置插件
- 点击 "重置插件" 按钮
- 确认对话框
- 清除所有数据，回到初始状态

## 🎨 设计特色

- **深色主题**: GitHub风格的现代化界面
- **响应式设计**: 适配不同屏幕尺寸
- **流畅动画**: 按钮悬停效果和状态转换
- **即时反馈**: Toast通知系统
- **自动化**: 定时刷新和自动接收

## 🔧 技术亮点

- **VSCode集成**: 原生WebView面板
- **安全策略**: CSP内容安全保护
- **模块化设计**: 清晰的代码结构
- **TypeScript**: 类型安全的开发体验
- **现代构建**: ESBuild快速编译

## 🚀 与demo的一致性

✅ **完全一致的用户体验**
- 相同的视觉设计和布局
- 相同的交互逻辑和功能
- 相同的用户操作流程
- 相同的反馈和提示系统

插件成功将demo目录的完整功能移植到VSCode环境中！
