{"name": "augmentstar", "displayName": "AugmentStar", "description": "augment插件的授权账户服务", "version": "0.0.1", "publisher": "augmentstar", "author": "AugmentStar Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yunzhong-code/cursor-star"}, "keywords": ["augment授权", "验证码", "邮箱", "开发工具"], "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "augmentstar.openTempMail", "title": "打开临时邮箱", "category": "AugmentStar", "icon": "$(mail)"}, {"command": "augmentstar.openSidebar", "title": "打开AugmentStar", "icon": "$(star)"}], "viewsContainers": {"activitybar": [{"id": "augmentstar-sidebar", "title": "AugmentStar", "icon": "$(star)"}]}, "views": {"augmentstar-sidebar": [{"id": "augmentstar-main", "name": "临时邮箱服务", "type": "webview"}]}, "menus": {"commandPalette": [{"command": "augmentstar.openTempMail", "when": "true"}], "view/title": [{"command": "augmentstar.openTempMail", "when": "view == augmentstar-main", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run package-secure", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run check-types && npm run lint && node esbuild.js --production", "package-secure": "npm run package && node obfuscate.js", "test-sqljs": "node test-sqljs.js", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/sql.js": "^1.4.9", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "terser": "^5.43.1", "typescript": "^5.8.3"}, "dependencies": {"sql.js": "^1.10.3"}}