# 🔧 VSCode插件调试指南

## 问题排查

如果F5无法启动插件，请按以下步骤排查：

### 1. 检查编译状态
```bash
cd yun
npm run compile
```
确保编译成功，没有错误信息。

### 2. 检查文件结构
确保以下文件存在：
- ✅ `dist/extension.js` - 编译后的主文件
- ✅ `package.json` - 插件配置
- ✅ `.vscode/launch.json` - 启动配置

### 3. 手动启动方法

#### 方法1: 使用命令面板
1. 在VSCode中打开 `yun` 文件夹
2. 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
3. 输入 "Tasks: Run Task"
4. 选择 "npm: compile"
5. 编译完成后，按 `F5` 或选择 "Run Extension"

#### 方法2: 使用调试面板
1. 在VSCode中打开 `yun` 文件夹
2. 点击左侧的调试图标 (🐛)
3. 在顶部下拉菜单中选择 "Run Extension"
4. 点击绿色播放按钮

#### 方法3: 使用终端
```bash
# 在yun目录下
npm run compile
code --extensionDevelopmentPath=.
```

### 4. 验证插件加载

启动成功后，会打开一个新的VSCode窗口，标题栏显示 `[Extension Development Host]`

在新窗口中：
1. 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
2. 输入 "打开临时邮箱" 或 "AugmentStar"
3. 应该能看到命令 "打开临时邮箱"
4. 选择命令，插件界面应该出现

### 5. 常见问题解决

#### 问题1: "找不到任务"
**解决**: 确保在正确的文件夹中打开VSCode
```bash
cd /Users/<USER>/Desktop/augment-star/yun
code .
```

#### 问题2: "编译失败"
**解决**: 检查依赖是否安装
```bash
npm install
npm run compile
```

#### 问题3: "插件未加载"
**解决**: 检查package.json中的activationEvents
```json
"activationEvents": []
```

#### 问题4: "命令未注册"
**解决**: 检查package.json中的contributes.commands
```json
"contributes": {
  "commands": [
    {
      "command": "augmentstar.openTempMail",
      "title": "打开临时邮箱"
    }
  ]
}
```

### 6. 调试输出

在Extension Development Host窗口中：
1. 按 `Ctrl+Shift+I` (或 `Cmd+Option+I`) 打开开发者工具
2. 在Console中查看是否有错误信息
3. 应该能看到: "AugmentStar 临时邮箱插件已激活!"

### 7. 替代启动方法

如果F5仍然不工作，可以使用以下命令直接启动：

```bash
# 方法1: 使用VSCode命令行
code --extensionDevelopmentPath=/Users/<USER>/Desktop/augment-star/yun

# 方法2: 使用npm脚本 (如果配置了)
npm run vscode:prepublish
```

### 8. 最终验证

插件成功启动后，您应该看到：
- 新的VSCode窗口标题包含 "[Extension Development Host]"
- 命令面板中有 "打开临时邮箱" 命令
- 执行命令后出现临时邮箱界面
- 界面与demo完全一致

## 🚀 成功启动后的测试步骤

1. **获取邮箱**: 点击"获取邮箱"按钮
2. **复制邮箱**: 点击复制按钮
3. **查看验证码**: 等待3秒自动显示
4. **复制验证码**: 点击验证码复制按钮
5. **刷新功能**: 测试手动刷新
6. **重置功能**: 测试重置插件

如果以上步骤都正常，说明插件已成功运行！
