// 测试关闭宿主进程功能
const { exec } = require('child_process');
const { promisify } = require('util');

async function testKillProcesses() {
    try {
        console.log('🔍 测试关闭宿主进程功能');
        
        const execAsync = promisify(exec);
        
        // 模拟查找VS Code相关进程
        console.log('\n📊 查找VS Code相关进程:');
        
        let command = '';
        if (process.platform === 'darwin' || process.platform === 'linux') {
            command = 'ps aux | grep -E "Visual Studio Code|Code Helper|Electron" | grep -v grep';
        } else if (process.platform === 'win32') {
            command = 'tasklist | findstr /I "Code.exe Electron.exe"';
        }
        
        const { stdout } = await execAsync(command);
        const lines = stdout.trim().split('\n').filter(line => line.length > 0);
        
        console.log(`找到 ${lines.length} 个相关进程:`);
        
        const processes = [];
        
        for (const line of lines) {
            if (process.platform === 'darwin' || process.platform === 'linux') {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 11) {
                    const pid = parts[1];
                    const command = parts.slice(10).join(' ');
                    
                    let type = 'unknown';
                    if (command.includes('Visual Studio Code.app/Contents/MacOS') || command.includes('code')) {
                        type = 'main';
                    } else if (command.includes('Code Helper (Renderer)')) {
                        type = 'renderer';
                    } else if (command.includes('Code Helper (Plugin)')) {
                        type = 'plugin';
                    } else if (command.includes('Code Helper (GPU)')) {
                        type = 'gpu';
                    } else if (command.includes('Code Helper')) {
                        type = 'helper';
                    }

                    processes.push({
                        pid: pid,
                        name: command.substring(0, 100),
                        type: type
                    });
                    
                    console.log(`  PID: ${pid} | Type: ${type} | Command: ${command.substring(0, 80)}...`);
                }
            } else if (process.platform === 'win32') {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 2) {
                    const name = parts[0];
                    const pid = parts[1];
                    
                    processes.push({
                        pid: pid,
                        name: name,
                        type: name.toLowerCase().includes('code') ? 'main' : 'helper'
                    });
                    
                    console.log(`  PID: ${pid} | Type: ${name.toLowerCase().includes('code') ? 'main' : 'helper'} | Name: ${name}`);
                }
            }
        }
        
        // 按类型排序
        processes.sort((a, b) => {
            const order = { 'plugin': 1, 'renderer': 2, 'helper': 3, 'gpu': 4, 'main': 5, 'unknown': 6 };
            return (order[a.type] || 6) - (order[b.type] || 6);
        });
        
        console.log('\n🔄 排序后的关闭顺序:');
        processes.forEach((proc, index) => {
            console.log(`  ${index + 1}. PID: ${proc.pid} | Type: ${proc.type}`);
        });
        
        console.log('\n⚠️  注意: 这只是测试脚本，不会实际关闭进程');
        console.log('💡 实际的关闭命令将是:');
        
        processes.forEach(proc => {
            let killCommand = '';
            if (process.platform === 'darwin' || process.platform === 'linux') {
                killCommand = `kill -TERM ${proc.pid} || kill -9 ${proc.pid}`;
            } else if (process.platform === 'win32') {
                killCommand = `taskkill /PID ${proc.pid} /F`;
            }
            console.log(`  ${killCommand}`);
        });
        
        console.log('\n✅ 测试完成');
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
    }
}

// 运行测试
testKillProcesses().then(() => {
    console.log('\n🏁 测试结束');
}).catch(error => {
    console.log('❌ 执行失败:', error);
});
