// 测试多窗口检测逻辑
const { exec } = require('child_process');
const { promisify } = require('util');

async function checkMultipleWindows() {
    try {
        console.log('🔍 检查VS Code多窗口状态');
        
        const execAsync = promisify(exec);
        
        // 检查VS Code相关进程
        const { stdout } = await execAsync('ps aux | grep -E "Visual Studio Code|Code Helper" | grep -v grep');
        const processes = stdout.trim().split('\n').filter(line => line.length > 0);
        
        console.log('\n📊 VS Code相关进程:');
        processes.forEach((process, index) => {
            const parts = process.split(/\s+/);
            const pid = parts[1];
            const command = parts.slice(10).join(' ');
            console.log(`  ${index + 1}. PID: ${pid} - ${command.substring(0, 100)}...`);
        });
        
        console.log(`\n📈 总进程数: ${processes.length}`);
        
        // 分析进程类型
        const mainProcesses = processes.filter(p => p.includes('MacOS/Electron') || p.includes('Visual Studio Code.app/Contents/MacOS'));
        const rendererProcesses = processes.filter(p => p.includes('Code Helper (Renderer)'));
        const pluginProcesses = processes.filter(p => p.includes('Code Helper (Plugin)'));
        const gpuProcesses = processes.filter(p => p.includes('Code Helper (GPU)'));
        
        console.log('\n🔍 进程分类:');
        console.log(`  主进程: ${mainProcesses.length}`);
        console.log(`  渲染进程: ${rendererProcesses.length}`);
        console.log(`  插件进程: ${pluginProcesses.length}`);
        console.log(`  GPU进程: ${gpuProcesses.length}`);
        
        // 估算窗口数量（每个窗口通常有1个渲染进程）
        const estimatedWindows = rendererProcesses.length;
        console.log(`\n💡 估算窗口数量: ${estimatedWindows}`);
        
        const hasMultipleWindows = estimatedWindows > 1;
        
        if (hasMultipleWindows) {
            console.log('⚠️  检测到多个VS Code窗口！');
            console.log('💡 建议：关闭其他窗口后再进行重置操作');
        } else {
            console.log('✅ 只有一个VS Code窗口运行');
        }
        
        return hasMultipleWindows;
        
    } catch (error) {
        console.log('❌ 检查失败:', error.message);
        return false;
    }
}

// 运行检查
checkMultipleWindows().then(result => {
    console.log(`\n🏁 检查结果: ${result ? '有多窗口' : '单窗口'}`);
}).catch(error => {
    console.log('❌ 执行失败:', error);
});
