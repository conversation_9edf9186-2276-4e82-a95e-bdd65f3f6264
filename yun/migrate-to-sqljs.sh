#!/bin/bash

# AugmentStar 迁移到sql.js脚本

echo "🔄 开始迁移到sql.js..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在yun目录下运行此脚本"
    echo "正确路径: /Users/<USER>/Desktop/augment-star/yun"
    exit 1
fi

# 清理旧的构建和依赖
echo "🧹 清理旧的构建..."
rm -rf dist/
rm -rf node_modules/
rm -f *.vsix

# 安装新的依赖
echo "📦 安装sql.js依赖..."
npm install

# 尝试安装类型定义（如果失败也继续）
echo "📦 尝试安装类型定义..."
npm install --save-dev @types/sql.js 2>/dev/null || echo "⚠️ @types/sql.js不可用，使用自定义类型定义"

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败!"
    exit 1
fi

# 测试sql.js功能
echo "🧪 测试sql.js功能..."
npm run test-sqljs
if [ $? -ne 0 ]; then
    echo "❌ sql.js测试失败!"
    exit 1
fi

# 编译插件
echo "🔨 编译插件..."
npm run package-secure
if [ $? -ne 0 ]; then
    echo "❌ 编译失败!"
    exit 1
fi

# 检查编译输出
if [ ! -f "dist/extension.js" ]; then
    echo "❌ 编译文件不存在!"
    exit 1
fi

echo "✅ 编译成功!"
echo "📦 文件大小: $(du -h dist/extension.js | cut -f1)"

# 打包插件
echo "📦 打包插件..."
vsce package --no-dependencies
if [ $? -ne 0 ]; then
    echo "❌ 打包失败!"
    exit 1
fi

# 查找生成的.vsix文件
VSIX_FILE=$(ls -t *.vsix 2>/dev/null | head -n1)

if [ -n "$VSIX_FILE" ]; then
    echo "✅ 打包成功!"
    echo "📦 生成文件: $VSIX_FILE"
    echo "📦 文件大小: $(du -h "$VSIX_FILE" | cut -f1)"
    echo ""
    echo "🎯 安装方法:"
    echo "1. 卸载旧版本: code --uninstall-extension augmentstar.augmentstar"
    echo "2. 安装新版本: code --install-extension \"$VSIX_FILE\""
    echo "3. 重启VSCode"
    echo ""
    echo "🧪 测试建议:"
    echo "1. 安装后测试侧边栏功能"
    echo "2. 测试重置插件功能"
    echo "3. 检查是否有SQLite相关错误"
    echo ""
    echo "✨ 迁移到sql.js完成!"
else
    echo "❌ 未找到生成的.vsix文件!"
    exit 1
fi
