# 🎯 个人中心和在线教程功能指南

## ✅ 新增功能

现在插件包含完整的个人中心页面和在线教程功能！

### 🏠 个人中心页面

#### 页面结构
```
┌─────────────────────────────────────────┐
│ ← 返回      个人信息           续费     │
├─────────────────────────────────────────┤
│  👤  <EMAIL>                   │
│      VIP 到期时间: 2025-10-17           │
├─────────────────────────────────────────┤
│  介绍                            查看   │
│  帮助/FAQ                        查看   │
│  联系我                          查看   │
├─────────────────────────────────────────┤
│  版本号              v0.0.1  检查更新   │
├─────────────────────────────────────────┤
│  退出登录                        退出   │
└─────────────────────────────────────────┘
```

#### 功能特色
- ✅ **用户信息卡片**：显示头像、邮箱、VIP到期时间
- ✅ **功能菜单**：介绍、帮助/FAQ、联系我
- ✅ **版本信息**：当前版本号、检查更新功能
- ✅ **退出登录**：清除用户数据并返回未登录状态

### 🌐 在线教程功能

#### 浏览器打开
- ✅ **点击在线教程按钮** → 自动在默认浏览器中打开教程网页
- ✅ **VSCode API集成** → 使用 `vscode.env.openExternal()` 打开外部链接
- ✅ **用户反馈** → 显示"正在打开在线教程..."提示

## 🎮 完整操作流程

### 1. 启动插件
```bash
cd /Users/<USER>/Desktop/augment-star/yun
npm run compile
code --extensionDevelopmentPath=.
```

### 2. 在Extension Development Host中
- 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
- 输入 "打开临时邮箱"
- 选择命令执行

### 3. 测试个人中心功能

#### 🔐 先登录
1. 点击"登录"按钮
2. 填写邮箱密码或注册新账号
3. 登录成功后右上角显示👤图标+V徽章

#### 👤 进入个人中心
1. **点击👤图标** → 跳转到个人中心页面
2. **查看用户信息** → 显示登录的邮箱地址
3. **浏览功能菜单** → 介绍、帮助、联系我

#### 🔄 个人中心操作
- **点击"← 返回"** → 返回主页面
- **点击"续费"** → 显示VIP续费提示
- **点击"介绍"** → 显示介绍功能开发中
- **点击"帮助/FAQ"** → 显示FAQ功能开发中
- **点击"联系我"** → 显示联系功能开发中
- **点击"检查更新"** → 模拟检查更新过程
- **点击"退出登录"** → 退出登录并返回主页

### 4. 测试在线教程功能

#### 🌐 浏览器打开
1. **在主页面点击"在线教程"按钮**
2. **预期结果**：
   - 显示"正在打开在线教程..."提示
   - 自动在默认浏览器中打开教程网页
   - 网页地址：https://github.com/microsoft/vscode-extension-samples

#### 📱 多平台支持
- ✅ **Windows**：在默认浏览器中打开
- ✅ **macOS**：在默认浏览器中打开
- ✅ **Linux**：在默认浏览器中打开

## 🎨 界面展示

### 个人中心页面
```
┌─────────────────────────────────────────┐
│ ← 返回      个人信息           续费     │
├─────────────────────────────────────────┤
│                                         │
│  👤  <EMAIL>                     │
│      VIP 到期时间: 2025-10-17           │
│                                         │
├─────────────────────────────────────────┤
│  介绍                            查看   │
├─────────────────────────────────────────┤
│  帮助/FAQ                        查看   │
├─────────────────────────────────────────┤
│  联系我                          查看   │
├─────────────────────────────────────────┤
│                                         │
│  版本号              v0.0.1  检查更新   │
│                                         │
├─────────────────────────────────────────┤
│                                         │
│  退出登录                        退出   │
│                                         │
└─────────────────────────────────────────┘
```

### 主页面在线教程
```
┌─────────────────────────────────────────┐
│              Augment Star         👤 V  │
├─────────────────────────────────────────┤
│  ...                                    │
│                                         │
│  📢 系统公告              📚 在线教程   │
│  ┌─────────────────────────────────────┐ │
│  │ 欢迎使用临时邮箱插件！              │ │
│  │ VIP用户享有更长的邮箱有效期...      │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔧 技术实现

### VSCode API集成
```javascript
// 在WebView中发送消息
vscode.postMessage({
    command: 'openExternal',
    url: tutorialUrl
});

// 在扩展主代码中处理消息
case 'openExternal':
    vscode.env.openExternal(vscode.Uri.parse(message.url));
    return;
```

### 页面状态管理
```javascript
// 页面切换
function showProfile() {
    showPage('profile-page');
}

// 用户信息更新
const profileEmail = document.getElementById('profile-email');
profileEmail.textContent = currentUser.email;
```

## 🧪 测试场景

### 场景1: 未登录用户点击个人中心
1. 未登录状态下点击👤图标
2. 显示"请先登录"提示
3. 自动跳转到登录页面

### 场景2: 已登录用户使用个人中心
1. 登录后点击👤图标
2. 跳转到个人中心页面
3. 显示正确的用户邮箱信息
4. 所有菜单项可点击并有反馈

### 场景3: 在线教程功能
1. 点击"在线教程"按钮
2. 显示加载提示
3. 浏览器自动打开教程网页

### 场景4: 个人中心退出登录
1. 在个人中心点击"退出登录"
2. 清除用户数据
3. 返回主页面，显示未登录状态

## ✨ 与Demo的一致性

现在插件完全实现了demo的个人中心功能：
- ✅ 相同的页面布局和设计
- ✅ 相同的用户信息显示
- ✅ 相同的功能菜单结构
- ✅ 相同的版本信息和退出登录
- ✅ 完整的页面切换逻辑

## 🎉 完成状态

**个人中心和在线教程功能已完全实现！**

现在您可以：
1. ✅ 点击👤图标跳转到个人中心页面
2. ✅ 查看完整的用户信息和功能菜单
3. ✅ 点击在线教程在浏览器中打开网页
4. ✅ 享受与demo完全一致的用户体验

插件现在提供完整的用户管理和帮助系统！
