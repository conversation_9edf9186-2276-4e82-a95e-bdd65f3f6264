# 🔧 故障排除指南

## F5无法启动插件的解决方案

### 🎯 快速解决方案

#### 方法1: 使用命令行直接启动
```bash
cd /Users/<USER>/Desktop/augment-star/yun
npm run compile
code --extensionDevelopmentPath=.
```

#### 方法2: 使用启动脚本
```bash
cd /Users/<USER>/Desktop/augment-star/yun
./start-debug.sh
```

#### 方法3: 手动步骤
1. 确保在正确目录: `/Users/<USER>/Desktop/augment-star/yun`
2. 在VSCode中打开此目录: `File > Open Folder`
3. 编译插件: `Ctrl+Shift+P` → `Tasks: Run Task` → `npm: compile`
4. 启动调试: `Ctrl+Shift+P` → `Debug: Start Debugging`

### 🔍 详细排查步骤

#### 1. 检查工作目录
确保VSCode在正确的目录中打开：
```bash
pwd
# 应该显示: /Users/<USER>/Desktop/augment-star/yun
```

#### 2. 检查文件结构
```
yun/
├── src/extension.ts ✅
├── package.json ✅
├── dist/extension.js ✅
├── .vscode/launch.json ✅
└── node_modules/ ✅
```

#### 3. 验证编译状态
```bash
npm run compile
# 应该看到: ✅ 编译成功
```

#### 4. 检查VSCode配置

**launch.json 应该包含:**
```json
{
  "name": "Run Extension",
  "type": "extensionHost",
  "request": "launch",
  "args": ["--extensionDevelopmentPath=${workspaceFolder}"],
  "outFiles": ["${workspaceFolder}/dist/**/*.js"],
  "preLaunchTask": "npm: compile"
}
```

#### 5. 验证插件配置

**package.json 应该包含:**
```json
{
  "main": "./dist/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "augmentstar.openTempMail",
        "title": "打开临时邮箱"
      }
    ]
  }
}
```

### 🚀 启动成功的标志

#### 1. 新窗口打开
- 标题栏显示: `[Extension Development Host]`
- 状态栏显示扩展开发模式

#### 2. 命令可用
- 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
- 输入 "打开临时邮箱"
- 应该看到命令出现

#### 3. 控制台输出
- 按 `Ctrl+Shift+I` (或 `Cmd+Option+I`) 打开开发者工具
- Console中应该显示: "AugmentStar 临时邮箱插件已激活!"

### 🐛 常见错误及解决方案

#### 错误1: "找不到模块"
```
解决方案:
cd yun
npm install
npm run compile
```

#### 错误2: "任务未找到"
```
解决方案:
检查 .vscode/tasks.json 是否存在
或直接使用: npm run compile
```

#### 错误3: "扩展未激活"
```
解决方案:
检查 package.json 中的 activationEvents
确保命令正确注册
```

#### 错误4: "WebView无法显示"
```
解决方案:
检查 CSP 配置
确保 HTML 内容正确
```

### 🎯 测试插件功能

启动成功后，测试以下功能：

1. **命令执行**
   - `Ctrl+Shift+P` → "打开临时邮箱"
   - 应该打开插件界面

2. **获取邮箱**
   - 点击 "获取邮箱" 按钮
   - 应该生成随机邮箱地址

3. **复制功能**
   - 点击 "复制" 按钮
   - 应该显示 "已复制到剪贴板"

4. **验证码功能**
   - 获取邮箱后等待3秒
   - 应该自动显示验证码

5. **刷新功能**
   - 点击 "刷新" 按钮
   - 应该生成新验证码

6. **重置功能**
   - 点击 "重置插件" 按钮
   - 确认后应该清除所有数据

### 📞 获取帮助

如果问题仍然存在：

1. **检查VSCode版本**: 确保使用最新版本
2. **重启VSCode**: 完全关闭并重新打开
3. **清理缓存**: 删除 `dist/` 文件夹后重新编译
4. **查看日志**: 开发者工具 → Console 查看错误信息

### ✅ 成功标志

当您看到以下界面时，说明插件已成功运行：

```
┌─────────────────────────────────────────┐
│              Augment Star               │
│                                  [登录] │
├─────────────────────────────────────────┤
│  📧 邮箱地址                            │
│  ┌─────────────────────────────────────┐ │
│  │ 点击获取邮箱按钮生成邮箱地址        │ │
│  └─────────────────────────────────────┘ │
│  [获取邮箱]              [重置插件]     │
│                                         │
│  🔢 验证码                    [刷新]    │
│  ┌─────────────────────────────────────┐ │
│  │           📭                        │
│  │        暂无验证码                   │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

🎉 **恭喜！插件已成功运行，您可以享受与demo完全一致的临时邮箱功能了！**
