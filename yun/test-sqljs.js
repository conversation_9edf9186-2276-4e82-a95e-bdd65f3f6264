#!/usr/bin/env node

// 测试sql.js功能的脚本

const fs = require('fs');
const path = require('path');

async function testSqlJs() {
    console.log('🧪 测试sql.js功能...');
    
    try {
        // 动态导入sql.js
        const initSqlJs = (await import('sql.js')).default;
        const SQL = await initSqlJs();
        
        console.log('✅ sql.js加载成功');
        
        // 创建测试数据库
        const db = new SQL.Database();
        
        // 创建测试表
        db.run("CREATE TABLE IF NOT EXISTS ItemTable (key TEXT PRIMARY KEY, value TEXT)");
        console.log('✅ 创建测试表成功');
        
        // 插入测试数据
        const testKeys = [
            'workbench.view.extension.augment-chat.state.hidden',
            'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}',
            'workbench.view.extension.augment-panel.state.hidden',
            'Augment.vscode-augment'
        ];
        
        for (const key of testKeys) {
            db.run("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)", [key, JSON.stringify({test: true})]);
        }
        console.log('✅ 插入测试数据成功');
        
        // 查询数据
        const stmt = db.prepare("SELECT key FROM ItemTable WHERE key LIKE ?");
        stmt.bind(['%augment%']);
        
        const results = [];
        while (stmt.step()) {
            results.push(stmt.getAsObject());
        }
        stmt.free();
        
        console.log(`✅ 查询到 ${results.length} 条记录`);
        
        // 删除数据
        let totalDeleted = 0;
        for (const key of testKeys) {
            db.run("DELETE FROM ItemTable WHERE key = ?", [key]);
            totalDeleted++;
        }
        console.log(`✅ 删除 ${totalDeleted} 条记录成功`);
        
        // 验证删除结果
        const remainingStmt = db.prepare("SELECT COUNT(*) as count FROM ItemTable WHERE key LIKE ?");
        remainingStmt.bind(['%augment%']);
        remainingStmt.step();
        const remaining = remainingStmt.getAsObject();
        remainingStmt.free();
        
        console.log(`✅ 剩余记录数: ${remaining.count}`);
        
        // 导出数据库
        const data = db.export();
        console.log(`✅ 数据库导出成功，大小: ${data.length} 字节`);
        
        db.close();
        console.log('✅ 数据库关闭成功');
        
        console.log('\n🎉 sql.js功能测试全部通过！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
testSqlJs().catch(console.error);
