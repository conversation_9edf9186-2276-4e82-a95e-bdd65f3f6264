#!/bin/bash

# AugmentStar 插件打包脚本

echo "🚀 开始打包 AugmentStar 插件..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在yun目录下运行此脚本"
    echo "正确路径: /Users/<USER>/Desktop/augment-star/yun"
    exit 1
fi

# 检查vsce是否已安装
if ! command -v vsce &> /dev/null; then
    echo "📦 安装vsce打包工具..."
    npm install -g vsce
    if [ $? -ne 0 ]; then
        echo "❌ vsce安装失败!"
        exit 1
    fi
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf dist/
rm -f *.vsix

# 安装依赖
echo "📦 安装依赖..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败!"
    exit 1
fi

# 编译和混淆插件
echo "🔨 编译和混淆插件..."
npm run package-secure
if [ $? -ne 0 ]; then
    echo "❌ 编译失败!"
    exit 1
fi

# 检查编译输出
if [ ! -f "dist/extension.js" ]; then
    echo "❌ 编译文件不存在!"
    exit 1
fi

echo "✅ 编译成功!"
echo "📦 文件大小: $(du -h dist/extension.js | cut -f1)"

# 打包插件
echo "📦 打包插件..."
vsce package --no-dependencies
if [ $? -ne 0 ]; then
    echo "❌ 打包失败!"
    exit 1
fi

# 查找生成的.vsix文件
VSIX_FILE=$(ls -t *.vsix 2>/dev/null | head -n1)

if [ -n "$VSIX_FILE" ]; then
    echo "✅ 打包成功!"
    echo "📦 生成文件: $VSIX_FILE"
    echo "📦 文件大小: $(du -h "$VSIX_FILE" | cut -f1)"
    echo ""
    echo "🎯 安装方法:"
    echo "1. 在VSCode中按 Ctrl+Shift+P"
    echo "2. 输入 'Extensions: Install from VSIX'"
    echo "3. 选择文件: $VSIX_FILE"
    echo ""
    echo "🚀 或者使用命令行安装:"
    echo "code --install-extension \"$VSIX_FILE\""
    echo ""
    echo "✨ 插件打包完成!"
else
    echo "❌ 未找到生成的.vsix文件!"
    exit 1
fi
