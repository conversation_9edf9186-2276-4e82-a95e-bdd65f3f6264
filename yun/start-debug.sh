#!/bin/bash

# AugmentStar 临时邮箱插件启动脚本

echo "🚀 启动 AugmentStar 临时邮箱插件调试..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在yun目录下运行此脚本"
    echo "正确路径: /Users/<USER>/Desktop/augment-star/yun"
    exit 1
fi

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 编译插件
echo "🔨 编译插件..."
npm run compile

if [ $? -eq 0 ]; then
    echo "✅ 编译成功!"
else
    echo "❌ 编译失败!"
    exit 1
fi

# 检查编译输出
if [ -f "dist/extension.js" ]; then
    echo "✅ 找到编译文件: dist/extension.js"
    echo "📦 文件大小: $(du -h dist/extension.js | cut -f1)"
else
    echo "❌ 编译文件不存在!"
    exit 1
fi

echo ""
echo "🎯 启动方法:"
echo "1. 在VSCode中打开此目录"
echo "2. 按 F5 启动扩展开发主机"
echo "3. 或者运行以下命令:"
echo "   code --extensionDevelopmentPath=."
echo ""
echo "📋 测试步骤:"
echo "1. 在新窗口中按 Ctrl+Shift+P (或 Cmd+Shift+P)"
echo "2. 输入 '打开临时邮箱'"
echo "3. 选择命令并享受功能!"
echo ""
echo "✨ 插件已准备就绪!"

# 可选: 直接启动VSCode
read -p "是否直接启动VSCode? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动VSCode..."
    code --extensionDevelopmentPath=.
fi
