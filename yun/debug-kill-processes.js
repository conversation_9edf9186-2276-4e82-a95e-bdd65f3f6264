// 调试进程关闭功能
const { exec } = require('child_process');
const { promisify } = require('util');

async function debugKillProcesses() {
    try {
        console.log('🔍 调试进程关闭功能');
        
        const execAsync = promisify(exec);
        
        // 第一步：查找所有VS Code进程
        console.log('\n📊 第一步：查找所有VS Code进程');
        
        let findCommand = '';
        if (process.platform === 'darwin' || process.platform === 'linux') {
            // 更精确的进程查找
            findCommand = 'ps aux | grep -E "(Visual Studio Code|Code Helper|Electron.*Code|/Applications/.*Code)" | grep -v grep';
        } else if (process.platform === 'win32') {
            findCommand = 'tasklist | findstr /I "Code.exe Electron.exe VSCode.exe"';
        }
        
        console.log(`执行命令: ${findCommand}`);
        const { stdout } = await execAsync(findCommand);
        const lines = stdout.trim().split('\n').filter(line => line.length > 0);
        
        console.log(`找到 ${lines.length} 个相关进程:`);
        
        const processes = [];
        
        for (const line of lines) {
            if (process.platform === 'darwin' || process.platform === 'linux') {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 11) {
                    const pid = parts[1];
                    const command = parts.slice(10).join(' ');
                    
                    // 更精确的进程分类
                    let type = 'unknown';
                    if (command.includes('Visual Studio Code.app/Contents/MacOS/Electron') || 
                        command.includes('/Applications/Visual Studio Code.app/Contents/MacOS/')) {
                        type = 'main';
                    } else if (command.includes('Code Helper (Renderer)')) {
                        type = 'renderer';
                    } else if (command.includes('Code Helper (Plugin)')) {
                        type = 'plugin';
                    } else if (command.includes('Code Helper (GPU)')) {
                        type = 'gpu';
                    } else if (command.includes('Code Helper')) {
                        type = 'helper';
                    } else if (command.includes('Visual Studio Code') || command.includes('Electron')) {
                        type = 'electron';
                    }

                    processes.push({
                        pid: pid,
                        name: command.substring(0, 120),
                        type: type,
                        fullCommand: command
                    });
                    
                    console.log(`  PID: ${pid.padEnd(8)} | Type: ${type.padEnd(10)} | ${command.substring(0, 80)}...`);
                }
            }
        }
        
        // 第二步：测试单个进程的kill命令
        console.log('\n🧪 第二步：测试kill命令（不实际执行）');
        
        for (const proc of processes.slice(0, 3)) { // 只测试前3个
            console.log(`\n测试进程 PID: ${proc.pid} (${proc.type})`);
            
            // 检查进程是否还存在
            try {
                const checkCmd = process.platform === 'win32' ? 
                    `tasklist /FI "PID eq ${proc.pid}"` : 
                    `ps -p ${proc.pid}`;
                
                const { stdout: checkResult } = await execAsync(checkCmd);
                console.log(`  ✅ 进程存在: ${checkResult.includes(proc.pid) ? '是' : '否'}`);
                
                // 生成kill命令
                let killCommands = [];
                if (process.platform === 'darwin' || process.platform === 'linux') {
                    killCommands = [
                        `kill -TERM ${proc.pid}`,
                        `kill -KILL ${proc.pid}`,
                        `kill -9 ${proc.pid}`
                    ];
                } else if (process.platform === 'win32') {
                    killCommands = [
                        `taskkill /PID ${proc.pid}`,
                        `taskkill /PID ${proc.pid} /F`
                    ];
                }
                
                console.log(`  💀 可用的kill命令:`);
                killCommands.forEach((cmd, i) => {
                    console.log(`    ${i + 1}. ${cmd}`);
                });
                
            } catch (error) {
                console.log(`  ❌ 检查进程失败: ${error.message}`);
            }
        }
        
        // 第三步：建议更强力的关闭方法
        console.log('\n💪 第三步：更强力的关闭方法');
        
        if (process.platform === 'darwin' || process.platform === 'linux') {
            console.log('  建议的系统级关闭命令:');
            console.log('    1. pkill -f "Visual Studio Code"');
            console.log('    2. pkill -f "Code Helper"');
            console.log('    3. killall "Visual Studio Code"');
            console.log('    4. killall "Code Helper (Renderer)"');
            console.log('    5. killall "Code Helper (Plugin)"');
            console.log('    6. killall "Code Helper (GPU)"');
        } else if (process.platform === 'win32') {
            console.log('  建议的系统级关闭命令:');
            console.log('    1. taskkill /IM "Code.exe" /F');
            console.log('    2. taskkill /IM "Electron.exe" /F');
            console.log('    3. wmic process where "name like \'%Code%\'" delete');
        }
        
        console.log('\n✅ 调试完成');
        console.log('\n💡 建议：');
        console.log('  1. 使用更强的kill信号 (-9, /F)');
        console.log('  2. 添加系统级关闭命令作为备选');
        console.log('  3. 增加重试机制');
        console.log('  4. 添加进程关闭后的验证');
        
    } catch (error) {
        console.log('❌ 调试失败:', error.message);
    }
}

// 运行调试
debugKillProcesses().then(() => {
    console.log('\n🏁 调试结束');
}).catch(error => {
    console.log('❌ 执行失败:', error);
});
