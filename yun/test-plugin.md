# AugmentStar 临时邮箱插件测试指南

## 插件功能

这个VSCode插件完全复制了demo目录中的临时邮箱功能，包括：

### 主要功能
1. **获取临时邮箱** - 点击"获取邮箱"按钮生成随机邮箱地址
2. **复制邮箱** - 一键复制生成的邮箱地址到剪贴板
3. **接收验证码** - 自动模拟接收验证码（3秒后显示）
4. **复制验证码** - 一键复制验证码到剪贴板
5. **刷新验证码** - 手动刷新获取新的验证码
6. **重置插件** - 清除所有数据，重置到初始状态

### 界面特色
- **现代化深色主题** - 与demo完全一致的GitHub深色风格
- **响应式布局** - 适配不同屏幕尺寸
- **流畅动画** - 按钮悬停效果和状态转换
- **Toast通知** - 操作反馈提示
- **自动刷新** - 每30秒自动检查新验证码

## 如何测试

### 1. 安装和运行
```bash
cd yun
npm install
npm run compile
```

### 2. 在VSCode中测试
1. 按 `F5` 启动扩展开发主机
2. 在新窗口中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
3. 输入 "打开临时邮箱" 或 "AugmentStar"
4. 选择命令运行

### 3. 功能测试步骤
1. **获取邮箱测试**
   - 点击"获取邮箱"按钮
   - 应该显示随机生成的邮箱地址
   - 复制按钮应该变为可用状态

2. **验证码测试**
   - 获取邮箱后等待3秒
   - 应该自动显示验证码
   - 点击复制按钮测试复制功能

3. **刷新功能测试**
   - 点击验证码区域的"刷新"按钮
   - 应该生成新的验证码

4. **重置功能测试**
   - 点击"重置插件"按钮
   - 确认对话框后应该清除所有数据

## 与demo的一致性

插件界面与demo目录下的页面完全一致：
- ✅ 相同的颜色方案和样式
- ✅ 相同的布局和组件
- ✅ 相同的交互逻辑
- ✅ 相同的功能特性
- ✅ 相同的用户体验

## 技术实现

- **框架**: VSCode Extension API
- **UI**: HTML + CSS (与demo完全相同的样式)
- **交互**: JavaScript (移植demo的所有功能)
- **安全**: CSP (内容安全策略) 保护
- **构建**: TypeScript + ESBuild

## 下一步扩展

基础功能已完成，可以进一步添加：
1. 登录/注册系统
2. VIP套餐功能
3. 个人中心页面
4. 支付系统集成
5. 真实的邮箱API集成

插件已成功将demo的完整功能移植到VSCode环境中！
