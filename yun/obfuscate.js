const fs = require('fs');
const path = require('path');
const { minify } = require('terser');

async function obfuscateCode() {
    const inputFile = path.join(__dirname, 'dist', 'extension.js');
    const outputFile = path.join(__dirname, 'dist', 'extension.obfuscated.js');
    
    if (!fs.existsSync(inputFile)) {
        console.error('❌ 编译文件不存在，请先运行 npm run package');
        process.exit(1);
    }
    
    console.log('🔒 开始代码混淆...');
    
    const code = fs.readFileSync(inputFile, 'utf8');
    
    const result = await minify(code, {
        compress: {
            dead_code: true,
            drop_console: true, // 移除console.log
            drop_debugger: true,
            keep_fargs: false,
            unused: true,
            join_vars: true,
            sequences: true,
            conditionals: true,
            comparisons: true,
            evaluate: true,
            booleans: true,
            loops: true,
            hoist_funs: true,
            hoist_vars: true,
            if_return: true,
            inline: true,
            reduce_vars: true,
            collapse_vars: true,
            pure_getters: true,
            pure_funcs: ['console.log', 'console.info', 'console.warn'],
        },
        mangle: {
            toplevel: true,
            eval: true,
            keep_fnames: false,
            properties: {
                regex: /^_/
            }
        },
        format: {
            comments: false,
            beautify: false,
            semicolons: true,
        },
        toplevel: true,
        nameCache: {},
        ie8: false,
        safari10: false,
    });
    
    if (result.error) {
        console.error('❌ 混淆失败:', result.error);
        process.exit(1);
    }
    
    // 替换原文件
    fs.writeFileSync(inputFile, result.code);
    
    console.log('✅ 代码混淆完成!');
    console.log(`📦 原始大小: ${(code.length / 1024).toFixed(2)} KB`);
    console.log(`📦 混淆后大小: ${(result.code.length / 1024).toFixed(2)} KB`);
    console.log(`📉 压缩率: ${((1 - result.code.length / code.length) * 100).toFixed(1)}%`);
}

obfuscateCode().catch(console.error);
