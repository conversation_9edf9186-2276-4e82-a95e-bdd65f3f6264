// 测试宿主检测逻辑
const path = require('path');
const fs = require('fs');

// 模拟 VS Code 的 globalStorageUri
const mockGlobalStoragePath = "/Users/<USER>/Library/Application Support/Code/User/globalStorage/augment.vscode-augment";

console.log('🔍 测试宿主检测逻辑');
console.log('模拟 globalStoragePath:', mockGlobalStoragePath);

// 解析路径
const pathParts = mockGlobalStoragePath.split(path.sep);
const globalStorageIndex = pathParts.findIndex(part => part === 'globalStorage');

console.log('\n📊 路径解析:');
console.log('pathParts:', pathParts);
console.log('globalStorageIndex:', globalStorageIndex);

if (globalStorageIndex > 0) {
    const userDataDir = pathParts.slice(0, globalStorageIndex).join(path.sep);
    const appSupportDir = path.dirname(userDataDir);
    const appName = path.basename(appSupportDir);
    
    console.log('\n✅ 解析结果:');
    console.log('userDataDir:', userDataDir);
    console.log('appSupportDir:', appSupportDir);
    console.log('appName:', appName);
    
    // 构建目标路径
    const machineIdPath = path.join(appSupportDir, 'machineid');
    const storageJsonPath = path.join(userDataDir, 'globalStorage', 'storage.json');
    
    console.log('\n🎯 目标文件路径:');
    console.log('machineIdPath:', machineIdPath);
    console.log('storageJsonPath:', storageJsonPath);
    
    // 检查文件是否存在
    console.log('\n📁 文件存在性检查:');
    console.log('machineIdPath 存在:', fs.existsSync(machineIdPath));
    console.log('storageJsonPath 存在:', fs.existsSync(storageJsonPath));
    
    // 如果 storage.json 存在，检查目标字段
    if (fs.existsSync(storageJsonPath)) {
        try {
            const content = fs.readFileSync(storageJsonPath, 'utf8');
            const data = JSON.parse(content);
            
            console.log('\n🔍 storage.json 中的目标字段:');
            const targetFields = [
                'storage.serviceMachineId',
                'telemetry.devDeviceId',
                'telemetry.macMachineId',
                'telemetry.machineId'
            ];
            
            targetFields.forEach(field => {
                const value = data[field];
                if (value !== undefined) {
                    console.log(`  ${field}: ${value.substring(0, 16)}...`);
                } else {
                    console.log(`  ${field}: [不存在]`);
                }
            });
        } catch (error) {
            console.log('❌ 解析 storage.json 失败:', error.message);
        }
    }
} else {
    console.log('❌ 无法从路径解析出 globalStorage 目录');
}
