// 配置文件 - 敏感信息保护
export class Config {
    // 使用简单的编码来隐藏API地址
    private static readonly _encodedProdUrl = '************************************************';
    private static readonly _encodedDevUrl = 'aHR0cDovL2xvY2FsaG9zdDo4MDgwL2N1cnNvci1hcGk=';
    
    public static getApiConfig() {
        const isProd = this._getEnvironment() === 'PRODUCTION';
        
        return {
            baseUrl: isProd ? this._decode(this._encodedProdUrl) : this._decode(this._encodedDevUrl),
            wsUrl: isProd ? 
                this._decode(this._encodedProdUrl).replace('http', 'ws') + '/ws' : 
                this._decode(this._encodedDevUrl).replace('http', 'ws') + '/ws'
        };
    }
    
    private static _decode(encoded: string): string {
        return Buffer.from(encoded, 'base64').toString('utf-8');
    }
    
    private static _getEnvironment(): string {
        // 可以通过环境变量或其他方式确定环境
        return 'PRODUCTION'; // 默认生产环境
    }
    
    // 其他配置
    public static readonly TIMEOUTS = {
        CONNECT: 15000,
        RECEIVE: 15000
    };
    
    public static readonly USER_LIMITS = {
        FREE_TOTAL: 6,
        VIP_DAILY: 3
    };
}
