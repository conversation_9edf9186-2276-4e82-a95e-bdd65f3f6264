import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';

/**
 * sql.js数据库操作工具类
 */
class SqlJsDatabase {
	private SQL: any;
	private db: any;

	constructor(SQL: any, dbPath: string) {
		this.SQL = SQL;
		// 读取现有数据库文件
		try {
			if (fs.existsSync(dbPath)) {
				const filebuffer = fs.readFileSync(dbPath);
				this.db = new SQL.Database(filebuffer);
			} else {
				this.db = new SQL.Database();
			}
		} catch (error) {
			// 如果读取文件失败，创建新的空数据库
			console.warn(`读取数据库文件失败，创建新数据库: ${error}`);
			this.db = new SQL.Database();
		}
	}

	// 执行查询，返回所有结果
	all(query: string, params: any[] = []): Promise<any[]> {
		return new Promise((resolve, reject) => {
			try {
				const stmt = this.db.prepare(query);
				const results: any[] = [];

				// 绑定参数并执行
				if (params.length > 0) {
					stmt.bind(params);
				}

				while (stmt.step()) {
					const row = stmt.getAsObject();
					results.push(row);
				}

				stmt.free();
				resolve(results);
			} catch (error) {
				reject(error);
			}
		});
	}

	// 执行更新/删除操作
	run(query: string, params: any[] = []): Promise<{changes: number}> {
		return new Promise((resolve, reject) => {
			try {
				const stmt = this.db.prepare(query);

				if (params.length > 0) {
					stmt.bind(params);
				}

				stmt.step();
				const changes = this.db.getRowsModified();
				stmt.free();

				resolve({changes});
			} catch (error) {
				reject(error);
			}
		});
	}

	// 保存数据库到文件
	saveToFile(dbPath: string): void {
		const data = this.db.export();
		fs.writeFileSync(dbPath, Buffer.from(data));
	}

	// 关闭数据库
	close(): Promise<void> {
		return new Promise((resolve) => {
			if (this.db) {
				this.db.close();
			}
			resolve();
		});
	}

	// 静态方法：创建数据库实例
	static async create(dbPath: string): Promise<SqlJsDatabase> {
		// 动态导入sql.js避免类型冲突
		const initSqlJs = (await import('sql.js')).default;
		const SQL = await initSqlJs();
		return new SqlJsDatabase(SQL, dbPath);
	}
}

export function activate(context: vscode.ExtensionContext) {

	// 临时恢复独立面板命令用于测试
	const disposable = vscode.commands.registerCommand('augmentstar.openTempMail', () => {
		TempMailPanel.createOrShow(context.extensionUri, context);
	});

	// 注册侧边栏视图提供者 - 侧边栏就是主页
	const sidebarProvider = new AugmentStarSidebarProvider(context.extensionUri, context);
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider('augmentstar-main', sidebarProvider),
		disposable
	);
}

export function deactivate() {}

/**
 * 侧边栏WebView提供者 - 直接显示完整主页内容
 */
class AugmentStarSidebarProvider implements vscode.WebviewViewProvider {
	public static readonly viewType = 'augmentstar-main';

	private _view?: vscode.WebviewView;

	constructor(
		private readonly _extensionUri: vscode.Uri,
		private readonly _context: vscode.ExtensionContext
	) {}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		this._view = webviewView;

		webviewView.webview.options = {
			enableScripts: true,
			localResourceRoots: [this._extensionUri]
		};

		// 使用完整的主页HTML内容
		webviewView.webview.html = this._getMainPageHtml(webviewView.webview);

		// 处理来自webview的消息 - 使用完整的主页消息处理逻辑
		webviewView.webview.onDidReceiveMessage(
			async message => {
				await this._handleWebviewMessage(message, webviewView.webview);
			},
			undefined,
			this._context.subscriptions
		);
	}

	// 生成完整主页HTML内容
	private _getMainPageHtml(webview: vscode.Webview): string {
		const nonce = this._getNonce();
		// 直接使用TempMailPanel的HTML生成逻辑
		return TempMailPanel.generateHtmlContent(nonce);
	}

	// 处理WebView消息 - 直接复制TempMailPanel的消息处理逻辑
	private async _handleWebviewMessage(message: any, webview: vscode.Webview): Promise<void> {
		switch (message.command) {
			case 'alert':
				vscode.window.showInformationMessage(message.text);
				return;
			case 'openExternal':
				vscode.env.openExternal(vscode.Uri.parse(message.url));
				return;
			case 'resetPlugin':
				// 重置插件功能 - 创建临时邮箱面板来处理
				TempMailPanel.createOrShow(this._extensionUri, this._context);
				return;
			case 'restartHost':
				// 重启宿主
				vscode.commands.executeCommand('workbench.action.reloadWindow');
				return;
			case 'killHostProcesses':
				// 关闭所有宿主进程 - 创建临时邮箱面板来处理
				TempMailPanel.createOrShow(this._extensionUri, this._context);
				// 通过静态方法调用
				if (TempMailPanel.currentPanel) {
					TempMailPanel.currentPanel.handleKillHostProcesses();
				}
				return;
			case 'apiRequest':
				try {
					// 使用VSCode的网络请求能力
					const response = await this.makeApiRequest(message.url, message.options);
					webview.postMessage({
						command: 'apiResponse',
						requestId: message.requestId,
						success: true,
						data: response
					});
				} catch (error) {
					const errorResponse: any = {
						command: 'apiResponse',
						requestId: message.requestId,
						success: false,
						error: error instanceof Error ? error.message : String(error)
					};

					// 如果错误对象包含响应数据，添加到错误响应中
					if (error && typeof error === 'object' && 'response' in error) {
						errorResponse.response = (error as any).response;
					}

					webview.postMessage(errorResponse);
				}
				return;
		}
	}

	private _getNonce() {
		let text = '';
		const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		for (let i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	// 处理API请求 - 复制自TempMailPanel
	private async makeApiRequest(url: string, options: any): Promise<any> {
		const https = require('https');
		const http = require('http');
		const urlModule = require('url');

		return new Promise((resolve, reject) => {
			const parsedUrl = urlModule.parse(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			const requestOptions = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.path,
				method: options.method || 'GET',
				headers: options.headers || {},
				timeout: 15000
			};

			const req = client.request(requestOptions, (res: any) => {
				let data = '';
				res.on('data', (chunk: any) => {
					data += chunk;
				});

				res.on('end', () => {
					try {
						// 解析响应数据，返回JSON格式
						let responseData;
						if (typeof data === 'string') {
							responseData = JSON.parse(data);
						} else {
							responseData = data;
						}

						if (res.statusCode >= 200 && res.statusCode < 300) {
							resolve(responseData);
						} else {
							const error = new Error(`HTTP ${res.statusCode}: ${data}`);
							(error as any).response = {
								status: res.statusCode,
								statusText: res.statusMessage,
								headers: res.headers,
								data: data
							};
							reject(error);
						}
					} catch (error) {
						reject(error instanceof Error ? error : new Error(String(error)));
					}
				});
			});

			req.on('error', (error: any) => {
				reject(error);
			});

			req.on('timeout', () => {
				req.destroy();
				reject(new Error('Request timeout'));
			});

			if (options.body) {
				req.write(options.body);
			}

			req.end();
		});
	}



	// 获取宿主环境信息 - 复制自TempMailPanel
	private _getHostInfo() {
		const globalStorageUri = this._context.globalStorageUri;
		const globalStoragePath = globalStorageUri.fsPath;

		const path = require('path');
		const os = require('os');
		const platform = os.platform();

		let appSupportDir = '';
		let appName = 'unknown';
		let userDataDir = '';

		// 从globalStoragePath反推应用支持目录
		const pathParts = globalStoragePath.split(path.sep);
		const globalStorageIndex = pathParts.findIndex(part => part === 'globalStorage');

		if (globalStorageIndex > 0) {
			userDataDir = pathParts.slice(0, globalStorageIndex).join(path.sep);
			appSupportDir = path.dirname(userDataDir);
			appName = path.basename(appSupportDir);
		} else {
			// 如果无法从路径解析，使用默认方法
			const homeDir = os.homedir();
			if (platform === 'darwin') {
				const libraryPath = path.join(homeDir, 'Library', 'Application Support');
				const possibleApps = ['Code', 'Cursor', 'VSCodium', 'Code - Insiders'];
				const fs = require('fs');

				for (const app of possibleApps) {
					const appPath = path.join(libraryPath, app);
					try {
						if (fs.existsSync(appPath)) {
							appSupportDir = appPath;
							appName = app;
							userDataDir = path.join(appPath, 'User');
							break;
						}
					} catch (error) {
						// 忽略错误，继续检查下一个
					}
				}
			}
		}

		// 构建路径
		const machineIdPath = path.join(appSupportDir, 'machineid');
		const workspaceStorageDir = path.join(userDataDir, 'workspaceStorage');

		console.log('🔍 _getHostInfo - 路径信息:');
		console.log('  - userDataDir:', userDataDir);
		console.log('  - machineIdPath:', machineIdPath);
		console.log('  - machineIdPath存在:', fs.existsSync(machineIdPath));

		return {
			platform,
			appSupportDir,
			appName,
			userDataDir,
			machineIdPath,
			workspaceStorageDir,
			globalStoragePath
		};
	}

	// 更新机器ID文件 - 完全复制自TempMailPanel
	private async updateMachineId(machineIdPath: string): Promise<void> {
		console.log('🔍 updateMachineId - 开始更新机器ID');
		console.log('🔍 updateMachineId - 文件路径:', machineIdPath);
		console.log('🔍 updateMachineId - 文件是否存在:', fs.existsSync(machineIdPath));

		if (fs.existsSync(machineIdPath)) {
			try {
				const currentContent = fs.readFileSync(machineIdPath, 'utf8');
				console.log('🔍 updateMachineId - 当前内容:', currentContent);
			} catch (readError) {
				console.log('🔍 updateMachineId - 读取当前内容失败:', readError);
			}
		}

		const newMachineId = this.generateHex64();
		console.log('🔍 updateMachineId - 新机器ID:', newMachineId);

		// 尝试多种方法写入文件
		const methods = [
			{ name: '直接写入', method: () => this.tryDirectWrite(machineIdPath, newMachineId) },
			{ name: '修改权限后写入', method: () => this.tryChmodAndWrite(machineIdPath, newMachineId) },
			{ name: '删除后写入', method: () => this.tryDeleteAndWrite(machineIdPath, newMachineId) },
			{ name: '原生脚本删除后写入', method: () => this.tryNativeScriptDelete(machineIdPath, newMachineId) }
		];

		let lastError: Error | null = null;

		for (const methodInfo of methods) {
			try {
				console.log(`🔍 updateMachineId - 尝试方法: ${methodInfo.name}`);
				await methodInfo.method();
				console.log(`✅ updateMachineId - 方法 ${methodInfo.name} 成功`);

				// 验证写入结果
				try {
					const verifyContent = fs.readFileSync(machineIdPath, 'utf8');
					console.log('🔍 updateMachineId - 验证写入结果:', verifyContent);
				} catch (verifyError) {
					console.log('🔍 updateMachineId - 验证失败:', verifyError);
				}

				return; // 成功则返回
			} catch (error) {
				console.log(`❌ updateMachineId - 方法 ${methodInfo.name} 失败:`, error);
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		console.log('❌ updateMachineId - 所有方法都失败');
		throw new Error(`更新机器ID失败: ${lastError?.message || '未知错误'}`);
	}

	// 删除工作区存储目录 - 完全复制自TempMailPanel
	private async removeWorkspaceStorage(workspaceStorageDir: string): Promise<void> {
		const fs = require('fs');

		if (!fs.existsSync(workspaceStorageDir)) {
			return;
		}

		// 尝试多种方法删除目录
		const methods = [
			() => this.tryDirectRemoveDir(workspaceStorageDir),
			() => this.tryChmodAndRemoveDir(workspaceStorageDir),
			() => this.tryNativeScriptRemoveDir(workspaceStorageDir)
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`删除工作区存储失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接删除目录 - 完全复制自TempMailPanel
	private async tryDirectRemoveDir(dirPath: string): Promise<void> {
		const fs = require('fs');
		fs.rmSync(dirPath, { recursive: true, force: true });
	}

	// 修改权限后删除目录 - 完全复制自TempMailPanel
	private async tryChmodAndRemoveDir(dirPath: string): Promise<void> {
		const fs = require('fs');
		const path = require('path');

		// 递归修改目录权限
		const chmodRecursive = (dir: string) => {
			try {
				fs.chmodSync(dir, 0o777);
				const items = fs.readdirSync(dir);
				for (const item of items) {
					const itemPath = path.join(dir, item);
					const stat = fs.statSync(itemPath);
					if (stat.isDirectory()) {
						chmodRecursive(itemPath);
					} else {
						fs.chmodSync(itemPath, 0o666);
					}
				}
			} catch (error) {
				// 忽略权限修改错误
			}
		};

		chmodRecursive(dirPath);
		fs.rmSync(dirPath, { recursive: true, force: true });
	}

	// 使用原生脚本删除目录 - 完全复制自TempMailPanel
	private async tryNativeScriptRemoveDir(dirPath: string): Promise<void> {
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		try {
			// 先尝试普通命令
			if (process.platform === 'darwin' || process.platform === 'linux') {
				// macOS/Linux: 按照 machineid 文件的处理方式，先移除扩展属性，再删除目录
				try {
					// 递归移除目录及其所有子项的 com.apple.provenance 扩展属性
					await execAsync(`find "${dirPath}" -type d -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`);
					await execAsync(`find "${dirPath}" -type f -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`);
					// 修改权限后删除
					await execAsync(`chmod -R 755 "${dirPath}"`);
					await execAsync(`rm -rf "${dirPath}"`);
				} catch (findError) {
					// 如果 find 命令失败，尝试直接删除
					await execAsync(`rm -rf "${dirPath}"`);
				}
			} else if (process.platform === 'win32') {
				// Windows: 移除只读属性后删除
				await execAsync(`attrib -R "${dirPath}" /S /D`);
				await execAsync(`rmdir /S /Q "${dirPath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error('需要管理员权限才能删除工作区存储目录');
			}

			// 使用管理员权限删除目录
			if (process.platform === 'darwin' || process.platform === 'linux') {
				// 使用sudo权限，按照 machineid 文件的处理方式
				const commands = [
					`echo "${password}" | sudo -S find "${dirPath}" -type d -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`,
					`echo "${password}" | sudo -S find "${dirPath}" -type f -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`,
					`echo "${password}" | sudo -S chmod -R 755 "${dirPath}" 2>/dev/null || true`,
					`echo "${password}" | sudo -S rm -rf "${dirPath}"`
				];

				for (const command of commands) {
					try {
						await execAsync(command);
					} catch (error) {
						// 前面的命令失败可以忽略，最后一个命令失败则抛出错误
						if (command.includes('rm -rf')) {
							throw new Error(`删除目录失败，请检查密码是否正确: ${error}`);
						}
					}
				}
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份运行
				const psCommand = `Remove-Item -Path "${dirPath}" -Recurse -Force`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}
	}

	// 删除状态备份文件 - 完全复制自TempMailPanel
	private async removeStateBackup(userDataDir: string): Promise<void> {
		const path = require('path');
		const fs = require('fs');

		const backupPath = path.join(userDataDir, 'state.vscdb.backup');

		if (!fs.existsSync(backupPath)) {
			return;
		}

		// 尝试多种方法删除文件
		const methods = [
			() => this.tryDirectRemoveFile(backupPath),
			() => this.tryChmodAndRemoveFile(backupPath),
			() => this.tryNativeScriptRemoveFile(backupPath, '状态备份文件')
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`删除状态备份失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接删除文件 - 完全复制自TempMailPanel
	private async tryDirectRemoveFile(filePath: string): Promise<void> {
		const fs = require('fs');
		fs.unlinkSync(filePath);
	}

	// 修改权限后删除文件 - 完全复制自TempMailPanel
	private async tryChmodAndRemoveFile(filePath: string): Promise<void> {
		const fs = require('fs');
		fs.chmodSync(filePath, 0o666);
		fs.unlinkSync(filePath);
	}

	// 使用原生脚本删除文件 - 完全复制自TempMailPanel
	private async tryNativeScriptRemoveFile(filePath: string, fileDescription: string): Promise<void> {
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		try {
			// 先尝试普通命令
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`rm -f "${filePath}"`);
			} else if (process.platform === 'win32') {
				await execAsync(`del /F /Q "${filePath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error(`需要管理员权限才能删除${fileDescription}`);
			}

			// 使用管理员权限删除文件
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`echo "${password}" | sudo -S rm -f "${filePath}"`);
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份运行
				const psCommand = `Remove-Item -Path "${filePath}" -Force`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}
	}

	// 清理状态数据库中的指定字段 - 完全复制自TempMailPanel
	private async cleanStateDatabase(userDataDir: string): Promise<void> {
		const path = require('path');
		const fs = require('fs');

		// state.vscdb 文件实际在 globalStorage 目录下，不是直接在 User 目录下
		const stateDbPath = path.join(userDataDir, 'globalStorage', 'state.vscdb');

		if (!fs.existsSync(stateDbPath)) {
			return;
		}

		// 尝试多种方法清理数据库
		const methods = [
			{ name: '直接清理', method: () => this.tryDirectCleanDatabase(stateDbPath) },
			{ name: '修改权限后清理', method: () => this.tryChmodAndCleanDatabase(stateDbPath) },
			{ name: '使用管理员权限清理', method: () => this.tryNativeScriptCleanDatabase(stateDbPath) }
		];

		let lastError: Error | null = null;

		for (const methodInfo of methods) {
			try {
				await methodInfo.method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}
		throw new Error(`清理状态数据库失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接清理数据库 - 完全复制自TempMailPanel
	private async tryDirectCleanDatabase(stateDbPath: string): Promise<void> {
		const fs = require('fs');

		// 要删除的键
		const keysToRemove = [
			'workbench.view.extension.augment-chat.state.hidden',
			'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}',
			'workbench.view.extension.augment-panel.state.hidden',
			'Augment.vscode-augment'
		];

		// 创建备份
		const backupPath = stateDbPath + '.backup.' + Date.now();
		let backupCreated = false;

		let db: any = null;

		try {
			// 验证数据库文件存在且可读
			if (!fs.existsSync(stateDbPath)) {
				throw new Error(`数据库文件不存在: ${stateDbPath}`);
			}

			// 创建备份
			try {
				fs.copyFileSync(stateDbPath, backupPath);
				backupCreated = true;
				console.log(`数据库备份已创建: ${backupPath}`);
			} catch (error) {
				console.warn('创建备份失败，继续执行:', error);
			}

			// 创建数据库连接
			db = await SqlJsDatabase.create(stateDbPath);

			// 验证数据库结构
			const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
			const hasItemTable = tables.some((table: any) => table.name === 'ItemTable');

			if (!hasItemTable) {
				throw new Error('数据库中未找到ItemTable表');
			}

			// 先查询这些键是否存在
			const placeholders = keysToRemove.map(() => '?').join(',');
			const checkQuery = `SELECT key FROM ItemTable WHERE key IN (${placeholders})`;
			const existingRows = await db.all(checkQuery, keysToRemove);

			console.log(`找到 ${existingRows.length} 个需要删除的键`);

			if (existingRows.length === 0) {
				console.log('没有找到需要删除的键，操作完成');
				return;
			}

			// 删除指定的键
			let totalDeleted = 0;
			const errors: string[] = [];

			for (const key of keysToRemove) {
				try {
					const result = await db.run('DELETE FROM ItemTable WHERE key = ?', [key]);
					if (result.changes > 0) {
						totalDeleted++;
						console.log(`成功删除键: ${key}`);
					}
				} catch (error) {
					const errorMsg = `删除键 ${key} 失败: ${error instanceof Error ? error.message : String(error)}`;
					console.warn(errorMsg);
					errors.push(errorMsg);
					// 继续删除其他键，不中断整个过程
				}
			}

			// 保存数据库更改
			db.saveToFile(stateDbPath);

			// 再次查询确认删除结果
			const remainingRows = await db.all(checkQuery, keysToRemove);
			console.log(`删除完成，剩余 ${remainingRows.length} 个键未删除`);

			if (totalDeleted > 0) {
				console.log(`成功删除 ${totalDeleted} 个键`);
			}

			// 如果有错误但部分成功，记录警告
			if (errors.length > 0 && totalDeleted > 0) {
				console.warn(`部分删除失败: ${errors.join('; ')}`);
			} else if (errors.length > 0) {
				throw new Error(`所有删除操作都失败: ${errors.join('; ')}`);
			}

		} catch (error) {
			// 如果操作失败且有备份，尝试恢复
			if (backupCreated && fs.existsSync(backupPath)) {
				try {
					fs.copyFileSync(backupPath, stateDbPath);
					console.log('已从备份恢复数据库');
				} catch (restoreError) {
					console.error('恢复备份失败:', restoreError);
				}
			}

			throw new Error(`数据库操作失败: ${error instanceof Error ? error.message : String(error)}`);
		} finally {
			// 确保数据库连接被关闭
			if (db) {
				await db.close();
			}

			// 清理备份文件（成功时）
			if (backupCreated && fs.existsSync(backupPath)) {
				try {
					// 延迟删除备份，给用户一些时间验证结果
					setTimeout(() => {
						if (fs.existsSync(backupPath)) {
							fs.unlinkSync(backupPath);
							console.log('备份文件已清理');
						}
					}, 30000); // 30秒后删除备份
				} catch (error) {
					console.warn('清理备份文件失败:', error);
				}
			}
		}
	}

	// 修改权限后清理数据库 - 完全复制自TempMailPanel
	private async tryChmodAndCleanDatabase(stateDbPath: string): Promise<void> {
		const fs = require('fs');
		fs.chmodSync(stateDbPath, 0o666);
		return this.tryDirectCleanDatabase(stateDbPath);
	}

	// 使用原生脚本处理数据库权限 - 完全复制自TempMailPanel
	private async tryNativeScriptCleanDatabase(stateDbPath: string): Promise<void> {
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		try {
			// 先尝试修改权限
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`chmod 666 "${stateDbPath}"`);
			} else if (process.platform === 'win32') {
				await execAsync(`attrib -R "${stateDbPath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error('需要管理员权限才能修改状态数据库');
			}

			// 使用管理员权限修改权限
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`echo "${password}" | sudo -S chmod 666 "${stateDbPath}"`);
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份移除只读属性
				const psCommand = `Set-ItemProperty -Path "${stateDbPath}" -Name IsReadOnly -Value $false`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}

		// 权限修改后，尝试清理数据库
		return this.tryDirectCleanDatabase(stateDbPath);
	}

	// 更新存储配置JSON - 完全复制自TempMailPanel
	private async updateStorageJson(userDataDir: string): Promise<void> {
		const path = require('path');
		const fs = require('fs');

		const storageJsonPath = path.join(userDataDir, 'globalStorage', 'storage.json');

		// 生成新的ID数据
		const newStorageData = {
			"storage.serviceMachineId": this.generateHex64(),
			"telemetry.devDeviceId": this.generateUUID(),
			"telemetry.macMachineId": this.generateHex64(),
			"telemetry.machineId": this.generateHex64()
		};

		// 尝试多种方法更新文件
		const methods = [
			() => this.tryDirectUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryChmodAndUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryDeleteAndUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryNativeScriptUpdateStorage(storageJsonPath, newStorageData)
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`更新存储配置失败: ${lastError?.message || '未知错误'}`);
	}

	// 方法1: 直接更新存储文件 - 完全复制自TempMailPanel
	private async tryDirectUpdateStorage(filePath: string, newData: any): Promise<void> {
		const fs = require('fs');
		const path = require('path');

		let storageData: any = {};

		// 读取现有配置
		if (fs.existsSync(filePath)) {
			const content = fs.readFileSync(filePath, 'utf8');
			try {
				storageData = JSON.parse(content);
			} catch (parseError) {
				storageData = {};
			}
		} else {
			// 确保目录存在
			const globalStorageDir = path.dirname(filePath);
			if (!fs.existsSync(globalStorageDir)) {
				fs.mkdirSync(globalStorageDir, { recursive: true });
			}
		}

		// 合并新数据
		Object.assign(storageData, newData);

		// 写入文件
		fs.writeFileSync(filePath, JSON.stringify(storageData, null, 2), 'utf8');
	}

	// 方法2: 修改权限后更新 - 完全复制自TempMailPanel
	private async tryChmodAndUpdateStorage(filePath: string, newData: any): Promise<void> {
		const fs = require('fs');

		if (fs.existsSync(filePath)) {
			fs.chmodSync(filePath, 0o666);
		}
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 方法3: 删除后重新创建 - 完全复制自TempMailPanel
	private async tryDeleteAndUpdateStorage(filePath: string, newData: any): Promise<void> {
		const fs = require('fs');

		if (fs.existsSync(filePath)) {
			fs.unlinkSync(filePath);
		}
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 方法4: 使用原生脚本强制更新 - 完全复制自TempMailPanel
	private async tryNativeScriptUpdateStorage(filePath: string, newData: any): Promise<void> {
		const fs = require('fs');
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		if (fs.existsSync(filePath)) {
			try {
				// 先尝试普通命令
				try {
					fs.chmodSync(filePath, 0o666);
					fs.unlinkSync(filePath);
				} catch (normalError) {
					// 普通命令失败，请求管理员权限
					const password = await this.getAdminPassword();

					if (!password) {
						throw new Error('需要管理员权限才能修改存储配置文件');
					}

					// 使用管理员权限删除文件
					if (process.platform === 'darwin' || process.platform === 'linux') {
						await execAsync(`echo "${password}" | sudo -S rm -f "${filePath}"`);
					} else if (process.platform === 'win32') {
						// Windows使用PowerShell以管理员身份运行
						const psCommand = `Remove-Item -Path "${filePath}" -Force`;
						await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
					}
				}
			} catch (deleteError) {
				throw new Error(`删除存储配置文件失败: ${deleteError}`);
			}
		}

		// 重新创建文件
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 管理员密码存储
	private _adminPassword: string | null = null;

	// 生成64位十六进制字符串 - 完全复制自TempMailPanel
	private generateHex64(): string {
		const crypto = require('crypto');
		return crypto.randomBytes(32).toString('hex');
	}

	// 生成UUID - 完全复制自TempMailPanel
	private generateUUID(): string {
		const crypto = require('crypto');
		return crypto.randomUUID();
	}

	// 获取管理员密码 - 完全复制自TempMailPanel
	private async getAdminPassword(): Promise<string> {
		if (this._adminPassword) {
			return this._adminPassword;
		}

		const password = await vscode.window.showInputBox({
			prompt: '需要管理员权限来删除受保护的文件，请输入管理员密码',
			password: true,
			placeHolder: '输入管理员密码'
		});

		if (!password) {
			throw new Error('需要管理员密码才能继续操作');
		}

		this._adminPassword = password;
		return password;
	}

	// 清除管理员密码 - 完全复制自TempMailPanel
	private clearAdminPassword(): void {
		this._adminPassword = null;
	}

	// 方法1: 直接写入 - 完全复制自TempMailPanel
	private async tryDirectWrite(filePath: string, content: string): Promise<void> {
		const fs = require('fs');
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法2: 修改权限后写入 - 完全复制自TempMailPanel
	private async tryChmodAndWrite(filePath: string, content: string): Promise<void> {
		const fs = require('fs');

		if (fs.existsSync(filePath)) {
			fs.chmodSync(filePath, 0o666);
		}
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法3: 删除文件后重新创建 - 完全复制自TempMailPanel
	private async tryDeleteAndWrite(filePath: string, content: string): Promise<void> {
		const fs = require('fs');

		if (fs.existsSync(filePath)) {
			fs.unlinkSync(filePath);
		}
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法4: 使用原生脚本强制删除 - 完全复制自TempMailPanel
	private async tryNativeScriptDelete(filePath: string, content: string): Promise<void> {
		const fs = require('fs');
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		if (fs.existsSync(filePath)) {
			try {
				// macOS/Linux: 使用sudo rm强制删除
				if (process.platform === 'darwin' || process.platform === 'linux') {
					// 先尝试普通命令
					try {
						await execAsync(`xattr -d com.apple.provenance "${filePath}"`);
						await execAsync(`chmod 666 "${filePath}"`);
						await execAsync(`rm -f "${filePath}"`);
					} catch (normalError) {
						// 普通命令失败，请求管理员权限
						const password = await this.getAdminPassword();

						if (!password) {
							throw new Error('需要管理员权限才能删除受保护的文件');
						}

						// 使用密码执行sudo命令
						const commands = [
							`echo "${password}" | sudo -S xattr -d com.apple.provenance "${filePath}" 2>/dev/null || true`,
							`echo "${password}" | sudo -S chmod 666 "${filePath}" 2>/dev/null || true`,
							`echo "${password}" | sudo -S rm -f "${filePath}"`
						];

						for (const command of commands) {
							try {
								await execAsync(command);
							} catch (error) {
								// 前两个命令失败可以忽略，最后一个命令失败则抛出错误
								if (command.includes('rm -f')) {
									throw new Error(`删除文件失败，请检查密码是否正确: ${error}`);
								}
							}
						}
					}
				}
				// Windows: 使用管理员权限
				else if (process.platform === 'win32') {
					try {
						// 先尝试普通命令
						await execAsync(`attrib -R "${filePath}"`);
						await execAsync(`del /F /Q "${filePath}"`);
					} catch (normalError) {
						// 普通命令失败，请求管理员权限
						const password = await this.getAdminPassword();

						if (!password) {
							throw new Error('需要管理员权限才能删除受保护的文件');
						}

						// Windows使用PowerShell以管理员身份运行
						const psCommand = `Remove-Item -Path "${filePath}" -Force`;
						await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
					}
				}
			} catch (deleteError) {
				// 如果系统命令也失败，抛出错误
				throw new Error(`原生脚本删除失败: ${deleteError}`);
			}
		}

		// 创建新文件
		fs.writeFileSync(filePath, content, 'utf8');
	}
}

/**
 * 临时邮箱面板类
 */
class TempMailPanel {
	public static currentPanel: TempMailPanel | undefined;
	public static readonly viewType = 'tempMail';

	private readonly _panel: vscode.WebviewPanel;
	private readonly _extensionUri: vscode.Uri;
	private readonly _context: vscode.ExtensionContext;
	private _disposables: vscode.Disposable[] = [];
	private _adminPassword: string | null = null; // 缓存管理员密码

	public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
		const column = vscode.window.activeTextEditor
			? vscode.window.activeTextEditor.viewColumn
			: undefined;

		// 如果已经有面板打开，则显示它
		if (TempMailPanel.currentPanel) {
			TempMailPanel.currentPanel._panel.reveal(column);
			return;
		}

		// 创建新的面板
		const panel = vscode.window.createWebviewPanel(
			TempMailPanel.viewType,
			'Augment Star - 临时邮箱',
			column || vscode.ViewColumn.One,
			{
				enableScripts: true,
				localResourceRoots: [extensionUri]
			}
		);

		TempMailPanel.currentPanel = new TempMailPanel(panel, extensionUri, context);
	}

	private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
		this._panel = panel;
		this._extensionUri = extensionUri;
		this._context = context;

		// 设置初始HTML内容
		this._update();

		// 监听面板关闭事件
		this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

		// 处理来自webview的消息
		this._panel.webview.onDidReceiveMessage(
			async message => {
				switch (message.command) {
					case 'alert':
						vscode.window.showInformationMessage(message.text);
						return;
					case 'openExternal':
						vscode.env.openExternal(vscode.Uri.parse(message.url));
						return;
					case 'resetPlugin':
						// 处理重置插件命令，输出宿主路径信息
						this.handleResetPlugin();
						return;
					case 'restartHost':
						// 处理重启宿主命令
						this.handleRestartHost();
						return;
					case 'killHostProcesses':
						// 处理关闭所有宿主进程命令
						this.handleKillHostProcesses();
						return;
					case 'apiRequest':
						try {
							// 使用VSCode的网络请求能力
							const response = await this.makeApiRequest(message.url, message.options);

							// 解析响应数据 - 前端期望的是JSON数据，不是HTTP响应对象
							let responseData;
							if (response && typeof response.data === 'string') {
								// response.data是JSON字符串，需要解析
								responseData = JSON.parse(response.data);
							} else if (response && response.data) {
								// response.data已经是对象
								responseData = response.data;
							} else {
								// 直接使用response
								responseData = response;
							}

							this._panel.webview.postMessage({
								command: 'apiResponse',
								requestId: message.requestId,
								success: true,
								data: responseData
							});
						} catch (error) {
							// 如果错误包含响应数据，传递给前端
							const errorResponse: any = {
								command: 'apiResponse',
								requestId: message.requestId,
								success: false,
								error: error instanceof Error ? error.message : String(error)
							};

							// 如果错误对象包含响应数据，添加到错误响应中
							if (error && typeof error === 'object' && 'response' in error) {
								errorResponse.response = (error as any).response;
							}

							this._panel.webview.postMessage(errorResponse);
						}
						return;
					case 'log':

						return;
				}
			},
			null,
			this._disposables
		);
	}

	// 处理重置插件命令
	private async handleResetPlugin() {
		try {
			const hostInfo = this.getHostInfo();

			// 检查是否有多个VS Code窗口运行
			const multiWindowWarning = await this.checkMultipleWindows();

			// 显示确认对话框
			const warningMessage = multiWindowWarning
				? '⚠️ 检测到多个VS Code窗口正在运行！\n\n为确保重置成功，请先关闭所有其他VS Code窗口，然后重新运行此操作。\n\n如果继续，其他窗口可能会覆盖重置结果。是否仍要继续？'
				: '此操作将重置VS Code环境数据，包括机器ID、工作区存储等。是否继续？';

			const confirm = await vscode.window.showWarningMessage(
				warningMessage,
				{ modal: true },
				'确认重置',
				'取消'
			);

			if (confirm !== '确认重置') {
				return;
			}

			// 显示进度
			await vscode.window.withProgress({
				location: vscode.ProgressLocation.Notification,
				title: "正在重置环境数据...",
				cancellable: false
			}, async (progress) => {

				progress.report({ increment: 20, message: "生成新的机器ID..." });
				await this.updateMachineId(hostInfo.machineIdPath);

				progress.report({ increment: 40, message: "删除工作区存储..." });
				await this.removeWorkspaceStorage(hostInfo.workspaceStorageDir);

				progress.report({ increment: 60, message: "删除状态备份文件..." });
				await this.removeStateBackup(hostInfo.userDataDir);

				progress.report({ increment: 80, message: "清理状态数据库指定字段..." });
				await this.cleanStateDatabase(hostInfo.userDataDir);

				progress.report({ increment: 90, message: "更新存储配置..." });
				await this.updateStorageJson(hostInfo.userDataDir);

				progress.report({ increment: 100, message: "重置完成" });
			});

			// 清除缓存的密码
			this.clearAdminPassword();

			// 重置完成后，询问用户是否关闭所有宿主进程
			const action = await vscode.window.showInformationMessage(
				'环境数据重置完成！现在将关闭所有宿主进程以确保更改生效。',
				{ modal: true },
				'关闭所有进程',
				'仅重启当前窗口'
			);

			if (action === '关闭所有进程') {
				// 延迟1秒后关闭所有进程
				setTimeout(() => {
					this.handleKillHostProcesses();
				}, 1000);
			} else if (action === '仅重启当前窗口') {
				// 传统的重启方式
				vscode.commands.executeCommand('workbench.action.reloadWindow');
			}

		} catch (error) {
			// 清除缓存的密码
			this.clearAdminPassword();

			const errorMsg = error instanceof Error ? error.message : String(error);

			vscode.window.showErrorMessage(`重置失败: ${errorMsg}`);
		}
	}

	// 处理重启宿主命令
	private async handleRestartHost() {
		try {
			// 显示重启确认对话框
			const confirm = await vscode.window.showWarningMessage(
				'即将重启宿主应用，所有未保存的工作将丢失。是否继续？',
				{ modal: true },
				'确认重启',
				'取消'
			);

			if (confirm !== '确认重启') {
				return;
			}

			// 显示重启提示
			vscode.window.showInformationMessage('正在重启宿主应用...');

			// 延迟1秒后执行重启命令
			setTimeout(() => {
				vscode.commands.executeCommand('workbench.action.reloadWindow');
			}, 1000);

		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			vscode.window.showErrorMessage(`重启失败: ${errorMsg}`);
		}
	}

	// 处理关闭所有宿主进程命令
	public async handleKillHostProcesses() {
		try {
			// 查找所有宿主进程
			const processes = await this.findAllHostProcesses();

			if (processes.length === 0) {
				vscode.window.showInformationMessage('未找到任何宿主进程');
				return;
			}

			// 显示确认对话框，列出将要关闭的进程
			const processInfo = processes.map(p => `PID: ${p.pid} - ${p.name}`).join('\n');
			const confirm = await vscode.window.showWarningMessage(
				`即将关闭以下宿主进程，所有未保存的工作将丢失：\n\n${processInfo}\n\n是否继续？`,
				{ modal: true },
				'确认关闭',
				'取消'
			);

			if (confirm !== '确认关闭') {
				return;
			}

			// 显示进度并关闭进程
			await vscode.window.withProgress({
				location: vscode.ProgressLocation.Notification,
				title: "正在关闭宿主进程...",
				cancellable: false
			}, async (progress) => {
				await this.killProcessesByPids(processes, progress);
			});

			vscode.window.showInformationMessage('所有宿主进程已关闭');

		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			vscode.window.showErrorMessage(`关闭进程失败: ${errorMsg}`);
		}
	}

	// 查找所有宿主进程
	private async findAllHostProcesses(): Promise<Array<{pid: string, name: string, type: string}>> {
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		try {
			let command = '';
			if (process.platform === 'darwin' || process.platform === 'linux') {
				// macOS/Linux: 查找VS Code相关进程
				command = 'ps aux | grep -E "Visual Studio Code|Code Helper|Electron" | grep -v grep';
			} else if (process.platform === 'win32') {
				// Windows: 查找VS Code相关进程
				command = 'tasklist | findstr /I "Code.exe Electron.exe"';
			}

			const { stdout } = await execAsync(command);
			const lines = stdout.trim().split('\n').filter((line: string) => line.length > 0);

			const processes: Array<{pid: string, name: string, type: string}> = [];

			for (const line of lines) {
				if (process.platform === 'darwin' || process.platform === 'linux') {
					// Unix格式: user pid %cpu %mem vsz rss tty stat start time command
					const parts = line.trim().split(/\s+/);
					if (parts.length >= 11) {
						const pid = parts[1];
						const command = parts.slice(10).join(' ');

						// 分类进程类型
						let type = 'unknown';
						if (command.includes('Visual Studio Code.app/Contents/MacOS') || command.includes('code')) {
							type = 'main';
						} else if (command.includes('Code Helper (Renderer)')) {
							type = 'renderer';
						} else if (command.includes('Code Helper (Plugin)')) {
							type = 'plugin';
						} else if (command.includes('Code Helper (GPU)')) {
							type = 'gpu';
						} else if (command.includes('Code Helper')) {
							type = 'helper';
						}

						processes.push({
							pid: pid,
							name: command.substring(0, 100),
							type: type
						});
					}
				} else if (process.platform === 'win32') {
					// Windows格式: ImageName PID SessionName SessionNumber MemUsage
					const parts = line.trim().split(/\s+/);
					if (parts.length >= 2) {
						const name = parts[0];
						const pid = parts[1];

						processes.push({
							pid: pid,
							name: name,
							type: name.toLowerCase().includes('code') ? 'main' : 'helper'
						});
					}
				}
			}

			// 按类型排序，确保正确的关闭顺序
			processes.sort((a, b) => {
				const order = { 'plugin': 1, 'renderer': 2, 'helper': 3, 'gpu': 4, 'main': 5, 'unknown': 6 };
				return (order[a.type as keyof typeof order] || 6) - (order[b.type as keyof typeof order] || 6);
			});

			console.log('🔍 找到的宿主进程:', processes);
			return processes;

		} catch (error) {
			console.log('❌ 查找进程失败:', error);
			return [];
		}
	}

	// 按PID关闭进程
	private async killProcessesByPids(processes: Array<{pid: string, name: string, type: string}>, progress: vscode.Progress<{message?: string, increment?: number}>) {
		const { exec } = require('child_process');
		const { promisify } = require('util');
		const execAsync = promisify(exec);

		const totalProcesses = processes.length;
		let processedCount = 0;

		for (const proc of processes) {
			try {
				progress.report({
					increment: (100 / totalProcesses),
					message: `关闭进程 ${proc.type}: ${proc.pid}`
				});

				let killCommand = '';
				if (process.platform === 'darwin' || process.platform === 'linux') {
					// 先尝试温和关闭，然后强制关闭
					killCommand = `kill -TERM ${proc.pid} 2>/dev/null || kill -9 ${proc.pid} 2>/dev/null`;
				} else if (process.platform === 'win32') {
					// Windows使用taskkill
					killCommand = `taskkill /PID ${proc.pid} /F`;
				}

				if (killCommand) {
					await execAsync(killCommand);
					console.log(`✅ 已关闭进程: ${proc.pid} (${proc.type})`);
				}

				processedCount++;

				// 在关闭每个进程后稍作延迟
				await new Promise(resolve => setTimeout(resolve, 100));

			} catch (error) {
				console.log(`❌ 关闭进程失败: ${proc.pid} - ${error}`);
				// 继续处理下一个进程，不中断整个流程
			}
		}

		console.log(`🏁 进程关闭完成: ${processedCount}/${totalProcesses}`);
	}

	// 获取宿主环境信息
	private getHostInfo() {
		// 使用真正的扩展上下文获取全局存储路径
		const globalStorageUri = this._context.globalStorageUri;
		const globalStoragePath = globalStorageUri.fsPath;

		// 解析路径获取应用支持目录
		const path = require('path');
		const os = require('os');
		const platform = os.platform();

		let appSupportDir = '';
		let appName = 'unknown';
		let userDataDir = '';

		// 从globalStoragePath反推应用支持目录
		// globalStoragePath 格式类似: /Users/<USER>/Library/Application Support/Code/User/globalStorage/extensionId
		const pathParts = globalStoragePath.split(path.sep);
		const globalStorageIndex = pathParts.findIndex(part => part === 'globalStorage');

		if (globalStorageIndex > 0) {
			// 向上两级: globalStorage -> User -> Code
			userDataDir = pathParts.slice(0, globalStorageIndex).join(path.sep);
			appSupportDir = path.dirname(userDataDir);
			appName = path.basename(appSupportDir);
		} else {
			// 如果无法从路径解析，使用默认方法
			const homeDir = os.homedir();
			if (platform === 'darwin') {
				const libraryPath = path.join(homeDir, 'Library', 'Application Support');
				const possibleApps = ['Code', 'Cursor', 'VSCodium', 'Code - Insiders'];
				const fs = require('fs');

				for (const app of possibleApps) {
					const appPath = path.join(libraryPath, app);
					try {
						if (fs.existsSync(appPath)) {
							appSupportDir = appPath;
							appName = app;
							userDataDir = path.join(appPath, 'User');
							break;
						}
					} catch (error) {
						// 忽略错误，继续检查下一个
					}
				}
			}
		}

		// 确定宿主类型
		let hostType: 'vscode' | 'cursor' | 'vscodium' | 'unknown' = 'unknown';
		if (appName === 'Code' || appName === 'Code - Insiders') {
			hostType = 'vscode';
		} else if (appName === 'Cursor') {
			hostType = 'cursor';
		} else if (appName === 'VSCodium') {
			hostType = 'vscodium';
		}

		return {
			type: hostType,
			name: vscode.env.appName,
			appName: appName,
			appSupportDir: appSupportDir,
			userDataDir: userDataDir,
			machineIdPath: path.join(appSupportDir, 'machineid'),
			workspaceStorageDir: path.join(userDataDir, 'workspaceStorage'),
			pluginGlobalStorage: globalStoragePath,
			platform: platform
		};
	}



	// 生成64位十六进制字符串
	private generateHex64(): string {
		const crypto = require('crypto');
		return crypto.randomBytes(32).toString('hex');
	}

	// 生成UUID
	private generateUUID(): string {
		const crypto = require('crypto');
		return crypto.randomUUID();
	}

	// 获取管理员密码（只询问一次）
	private async getAdminPassword(): Promise<string | null> {
		if (this._adminPassword) {
			return this._adminPassword;
		}

		// Windows系统提示不同
		const isWindows = process.platform === 'win32';
		const prompt = isWindows
			? '需要管理员权限来修改受保护的文件。Windows将弹出UAC提示，请点击"是"：'
			: '需要管理员权限来修改受保护的文件，请输入您的系统密码：';

		const placeholder = isWindows ? '点击确定后将弹出UAC提示' : '输入系统密码';

		const password = await vscode.window.showInputBox({
			prompt: prompt,
			password: !isWindows, // Windows不需要密码输入
			placeHolder: placeholder,
			ignoreFocusOut: true
		});

		if (password !== undefined) { // 用户没有取消
			this._adminPassword = password || 'windows-uac'; // Windows使用特殊标记
		}

		return this._adminPassword;
	}

	// 清除缓存的密码
	private clearAdminPassword(): void {
		this._adminPassword = null;
	}

	// 更新机器ID文件
	private async updateMachineId(machineIdPath: string): Promise<void> {
		const newMachineId = this.generateHex64();

		// 尝试多种方法写入文件
		const methods = [
			() => this.tryDirectWrite(machineIdPath, newMachineId),
			() => this.tryChmodAndWrite(machineIdPath, newMachineId),
			() => this.tryDeleteAndWrite(machineIdPath, newMachineId),
			() => this.tryNativeScriptDelete(machineIdPath, newMachineId)
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`更新机器ID失败: ${lastError?.message || '未知错误'}`);
	}

	// 方法1: 直接写入
	private async tryDirectWrite(filePath: string, content: string): Promise<void> {
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法2: 修改权限后写入
	private async tryChmodAndWrite(filePath: string, content: string): Promise<void> {
		if (fs.existsSync(filePath)) {
			fs.chmodSync(filePath, 0o666);
		}
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法3: 删除文件后重新创建
	private async tryDeleteAndWrite(filePath: string, content: string): Promise<void> {
		if (fs.existsSync(filePath)) {
			fs.unlinkSync(filePath);
		}
		fs.writeFileSync(filePath, content, 'utf8');
	}

	// 方法4: 使用原生脚本强制删除
	private async tryNativeScriptDelete(filePath: string, content: string): Promise<void> {
		const execAsync = promisify(exec);

		if (fs.existsSync(filePath)) {
			try {
				// macOS/Linux: 使用sudo rm强制删除
				if (process.platform === 'darwin' || process.platform === 'linux') {
					// 先尝试普通命令
					try {
						await execAsync(`xattr -d com.apple.provenance "${filePath}"`);
						await execAsync(`chmod 666 "${filePath}"`);
						await execAsync(`rm -f "${filePath}"`);
					} catch (normalError) {
						// 普通命令失败，请求管理员权限
						const password = await this.getAdminPassword();

						if (!password) {
							throw new Error('需要管理员权限才能删除受保护的文件');
						}

						// 使用密码执行sudo命令
						const commands = [
							`echo "${password}" | sudo -S xattr -d com.apple.provenance "${filePath}" 2>/dev/null || true`,
							`echo "${password}" | sudo -S chmod 666 "${filePath}" 2>/dev/null || true`,
							`echo "${password}" | sudo -S rm -f "${filePath}"`
						];

						for (const command of commands) {
							try {
								await execAsync(command);
							} catch (error) {
								// 前两个命令失败可以忽略，最后一个命令失败则抛出错误
								if (command.includes('rm -f')) {
									throw new Error(`删除文件失败，请检查密码是否正确: ${error}`);
								}
							}
						}
					}
				}
				// Windows: 使用管理员权限
				else if (process.platform === 'win32') {
					try {
						// 先尝试普通命令
						await execAsync(`attrib -R "${filePath}"`);
						await execAsync(`del /F /Q "${filePath}"`);
					} catch (normalError) {
						// 普通命令失败，请求管理员权限
						const password = await this.getAdminPassword();

						if (!password) {
							throw new Error('需要管理员权限才能删除受保护的文件');
						}

						// Windows使用PowerShell以管理员身份运行
						const psCommand = `Remove-Item -Path "${filePath}" -Force`;
						await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
					}
				}
			} catch (deleteError) {
				// 如果系统命令也失败，抛出错误
				throw new Error(`原生脚本删除失败: ${deleteError}`);
			}
		}

		// 创建新文件
		fs.writeFileSync(filePath, content, 'utf8');
	}



	// 删除工作区存储目录
	private async removeWorkspaceStorage(workspaceStorageDir: string): Promise<void> {
		if (!fs.existsSync(workspaceStorageDir)) {
			return;
		}

		// 尝试多种方法删除目录
		const methods = [
			() => this.tryDirectRemoveDir(workspaceStorageDir),
			() => this.tryChmodAndRemoveDir(workspaceStorageDir),
			() => this.tryNativeScriptRemoveDir(workspaceStorageDir)
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`删除工作区存储失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接删除目录
	private async tryDirectRemoveDir(dirPath: string): Promise<void> {
		fs.rmSync(dirPath, { recursive: true, force: true });
	}

	// 修改权限后删除目录
	private async tryChmodAndRemoveDir(dirPath: string): Promise<void> {
		// 递归修改目录权限
		const chmodRecursive = (dir: string) => {
			try {
				fs.chmodSync(dir, 0o777);
				const items = fs.readdirSync(dir);
				for (const item of items) {
					const itemPath = path.join(dir, item);
					const stat = fs.statSync(itemPath);
					if (stat.isDirectory()) {
						chmodRecursive(itemPath);
					} else {
						fs.chmodSync(itemPath, 0o666);
					}
				}
			} catch (error) {
				// 忽略权限修改错误
			}
		};

		chmodRecursive(dirPath);
		fs.rmSync(dirPath, { recursive: true, force: true });
	}

	// 使用原生脚本删除目录
	private async tryNativeScriptRemoveDir(dirPath: string): Promise<void> {
		const execAsync = promisify(exec);

		try {
			// 先尝试普通命令
			if (process.platform === 'darwin' || process.platform === 'linux') {
				// macOS/Linux: 按照 machineid 文件的处理方式，先移除扩展属性，再删除目录
				try {
					// 递归移除目录及其所有子项的 com.apple.provenance 扩展属性
					await execAsync(`find "${dirPath}" -type d -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`);
					await execAsync(`find "${dirPath}" -type f -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`);
					// 修改权限后删除
					await execAsync(`chmod -R 755 "${dirPath}"`);
					await execAsync(`rm -rf "${dirPath}"`);
				} catch (findError) {
					// 如果 find 命令失败，尝试直接删除
					await execAsync(`rm -rf "${dirPath}"`);
				}
			} else if (process.platform === 'win32') {
				// Windows: 移除只读属性后删除
				await execAsync(`attrib -R "${dirPath}" /S /D`);
				await execAsync(`rmdir /S /Q "${dirPath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error('需要管理员权限才能删除工作区存储目录');
			}

			// 使用管理员权限删除目录
			if (process.platform === 'darwin' || process.platform === 'linux') {
				// 使用sudo权限，按照 machineid 文件的处理方式
				const commands = [
					`echo "${password}" | sudo -S find "${dirPath}" -type d -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`,
					`echo "${password}" | sudo -S find "${dirPath}" -type f -exec xattr -d com.apple.provenance {} \\; 2>/dev/null || true`,
					`echo "${password}" | sudo -S chmod -R 755 "${dirPath}" 2>/dev/null || true`,
					`echo "${password}" | sudo -S rm -rf "${dirPath}"`
				];

				for (const command of commands) {
					try {
						await execAsync(command);
					} catch (error) {
						// 前面的命令失败可以忽略，最后一个命令失败则抛出错误
						if (command.includes('rm -rf')) {
							throw new Error(`删除目录失败，请检查密码是否正确: ${error}`);
						}
					}
				}
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份运行
				const psCommand = `Remove-Item -Path "${dirPath}" -Recurse -Force`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}
	}

	// 删除状态备份文件
	private async removeStateBackup(userDataDir: string): Promise<void> {
		const backupPath = path.join(userDataDir, 'state.vscdb.backup');

		if (!fs.existsSync(backupPath)) {
			return;
		}

		// 尝试多种方法删除文件
		const methods = [
			() => this.tryDirectRemoveFile(backupPath),
			() => this.tryChmodAndRemoveFile(backupPath),
			() => this.tryNativeScriptRemoveFile(backupPath, '状态备份文件')
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`删除状态备份失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接删除文件
	private async tryDirectRemoveFile(filePath: string): Promise<void> {
		fs.unlinkSync(filePath);
	}

	// 修改权限后删除文件
	private async tryChmodAndRemoveFile(filePath: string): Promise<void> {
		fs.chmodSync(filePath, 0o666);
		fs.unlinkSync(filePath);
	}

	// 使用原生脚本删除文件
	private async tryNativeScriptRemoveFile(filePath: string, fileDescription: string): Promise<void> {
		const execAsync = promisify(exec);

		try {
			// 先尝试普通命令
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`rm -f "${filePath}"`);
			} else if (process.platform === 'win32') {
				await execAsync(`del /F /Q "${filePath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error(`需要管理员权限才能删除${fileDescription}`);
			}

			// 使用管理员权限删除文件
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`echo "${password}" | sudo -S rm -f "${filePath}"`);
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份运行
				const psCommand = `Remove-Item -Path "${filePath}" -Force`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}
	}

	// 清理状态数据库中的指定字段
	private async cleanStateDatabase(userDataDir: string): Promise<void> {
		// state.vscdb 文件实际在 globalStorage 目录下，不是直接在 User 目录下
		const stateDbPath = path.join(userDataDir, 'globalStorage', 'state.vscdb');

		if (!fs.existsSync(stateDbPath)) {
			return;
		}

		// 尝试多种方法清理数据库
		const methods = [
			{ name: '直接清理', method: () => this.tryDirectCleanDatabase(stateDbPath) },
			{ name: '修改权限后清理', method: () => this.tryChmodAndCleanDatabase(stateDbPath) },
			{ name: '使用管理员权限清理', method: () => this.tryNativeScriptCleanDatabase(stateDbPath) }
		];

		let lastError: Error | null = null;

		for (const methodInfo of methods) {
			try {
				await methodInfo.method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}
		throw new Error(`清理状态数据库失败: ${lastError?.message || '未知错误'}`);
	}

	// 直接清理数据库
	private async tryDirectCleanDatabase(stateDbPath: string): Promise<void> {
		// 要删除的键
		const keysToRemove = [
			'workbench.view.extension.augment-chat.state.hidden',
			'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}',
			'workbench.view.extension.augment-panel.state.hidden',
			'Augment.vscode-augment'
		];

		// 创建备份
		const backupPath = stateDbPath + '.backup.' + Date.now();
		let backupCreated = false;

		let db: SqlJsDatabase | null = null;

		try {
			// 验证数据库文件存在且可读
			if (!fs.existsSync(stateDbPath)) {
				throw new Error(`数据库文件不存在: ${stateDbPath}`);
			}

			// 创建备份
			try {
				fs.copyFileSync(stateDbPath, backupPath);
				backupCreated = true;
				console.log(`数据库备份已创建: ${backupPath}`);
			} catch (error) {
				console.warn('创建备份失败，继续执行:', error);
			}

			// 创建数据库连接
			db = await SqlJsDatabase.create(stateDbPath);

			// 验证数据库结构
			const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
			const hasItemTable = tables.some((table: any) => table.name === 'ItemTable');

			if (!hasItemTable) {
				throw new Error('数据库中未找到ItemTable表');
			}

			// 先查询这些键是否存在
			const placeholders = keysToRemove.map(() => '?').join(',');
			const checkQuery = `SELECT key FROM ItemTable WHERE key IN (${placeholders})`;
			const existingRows = await db.all(checkQuery, keysToRemove);

			console.log(`找到 ${existingRows.length} 个需要删除的键`);

			if (existingRows.length === 0) {
				console.log('没有找到需要删除的键，操作完成');
				return;
			}

			// 删除指定的键
			let totalDeleted = 0;
			const errors: string[] = [];

			for (const key of keysToRemove) {
				try {
					const result = await db.run('DELETE FROM ItemTable WHERE key = ?', [key]);
					if (result.changes > 0) {
						totalDeleted++;
						console.log(`成功删除键: ${key}`);
					}
				} catch (error) {
					const errorMsg = `删除键 ${key} 失败: ${error instanceof Error ? error.message : String(error)}`;
					console.warn(errorMsg);
					errors.push(errorMsg);
					// 继续删除其他键，不中断整个过程
				}
			}

			// 保存数据库更改
			db.saveToFile(stateDbPath);

			// 再次查询确认删除结果
			const remainingRows = await db.all(checkQuery, keysToRemove);
			console.log(`删除完成，剩余 ${remainingRows.length} 个键未删除`);

			if (totalDeleted > 0) {
				console.log(`成功删除 ${totalDeleted} 个键`);
			}

			// 如果有错误但部分成功，记录警告
			if (errors.length > 0 && totalDeleted > 0) {
				console.warn(`部分删除失败: ${errors.join('; ')}`);
			} else if (errors.length > 0) {
				throw new Error(`所有删除操作都失败: ${errors.join('; ')}`);
			}

		} catch (error) {
			// 如果操作失败且有备份，尝试恢复
			if (backupCreated && fs.existsSync(backupPath)) {
				try {
					fs.copyFileSync(backupPath, stateDbPath);
					console.log('已从备份恢复数据库');
				} catch (restoreError) {
					console.error('恢复备份失败:', restoreError);
				}
			}

			throw new Error(`数据库操作失败: ${error instanceof Error ? error.message : String(error)}`);
		} finally {
			// 确保数据库连接被关闭
			if (db) {
				await db.close();
			}

			// 清理备份文件（成功时）
			if (backupCreated && fs.existsSync(backupPath)) {
				try {
					// 延迟删除备份，给用户一些时间验证结果
					setTimeout(() => {
						if (fs.existsSync(backupPath)) {
							fs.unlinkSync(backupPath);
							console.log('备份文件已清理');
						}
					}, 30000); // 30秒后删除备份
				} catch (error) {
					console.warn('清理备份文件失败:', error);
				}
			}
		}
	}

	// 修改权限后清理数据库
	private async tryChmodAndCleanDatabase(stateDbPath: string): Promise<void> {
		fs.chmodSync(stateDbPath, 0o666);
		return this.tryDirectCleanDatabase(stateDbPath);
	}

	// 使用原生脚本处理数据库权限
	private async tryNativeScriptCleanDatabase(stateDbPath: string): Promise<void> {
		const execAsync = promisify(exec);

		try {
			// 先尝试修改权限
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`chmod 666 "${stateDbPath}"`);
			} else if (process.platform === 'win32') {
				await execAsync(`attrib -R "${stateDbPath}"`);
			}
		} catch (normalError) {
			// 普通命令失败，请求管理员权限
			const password = await this.getAdminPassword();

			if (!password) {
				throw new Error('需要管理员权限才能修改状态数据库');
			}

			// 使用管理员权限修改权限
			if (process.platform === 'darwin' || process.platform === 'linux') {
				await execAsync(`echo "${password}" | sudo -S chmod 666 "${stateDbPath}"`);
			} else if (process.platform === 'win32') {
				// Windows使用PowerShell以管理员身份移除只读属性
				const psCommand = `Set-ItemProperty -Path "${stateDbPath}" -Name IsReadOnly -Value $false`;
				await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
			}
		}

		// 权限修改后，尝试清理数据库
		return this.tryDirectCleanDatabase(stateDbPath);
	}

	// 更新存储配置JSON
	private async updateStorageJson(userDataDir: string): Promise<void> {
		const storageJsonPath = path.join(userDataDir, 'globalStorage', 'storage.json');
		console.log('🔍 updateStorageJson - 文件路径:', storageJsonPath);
		console.log('🔍 updateStorageJson - 文件是否存在:', fs.existsSync(storageJsonPath));

		// 生成新的ID数据
		const currentTime = new Date().toISOString();
		const newStorageData = {
			"storage.serviceMachineId": this.generateHex64(),
			"telemetry.devDeviceId": this.generateUUID(),
			"telemetry.macMachineId": this.generateHex64(),
			"telemetry.machineId": this.generateHex64(),
			"augment.testUpdate": `插件更新测试 - ${currentTime}`,
			"augment.updateCount": (Date.now() % 10000).toString()
		};
		console.log('🔍 updateStorageJson - 新数据:', newStorageData);

		// 尝试多种方法更新文件
		const methods = [
			() => this.tryDirectUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryChmodAndUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryDeleteAndUpdateStorage(storageJsonPath, newStorageData),
			() => this.tryNativeScriptUpdateStorage(storageJsonPath, newStorageData)
		];

		let lastError: Error | null = null;

		for (const method of methods) {
			try {
				await method();
				return; // 成功则返回
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				continue; // 失败则尝试下一种方法
			}
		}

		// 所有方法都失败
		throw new Error(`更新存储配置失败: ${lastError?.message || '未知错误'}`);
	}

	// 方法1: 直接更新存储文件
	private async tryDirectUpdateStorage(filePath: string, newData: any): Promise<void> {
		console.log('🔍 tryDirectUpdateStorage - 开始更新:', filePath);
		let storageData: any = {};

		// 读取现有配置
		if (fs.existsSync(filePath)) {
			console.log('🔍 tryDirectUpdateStorage - 文件存在，读取现有内容');
			const content = fs.readFileSync(filePath, 'utf8');
			console.log('🔍 tryDirectUpdateStorage - 现有内容:', content.substring(0, 200));
			try {
				storageData = JSON.parse(content);
				console.log('🔍 tryDirectUpdateStorage - 解析成功，现有数据键:', Object.keys(storageData));
			} catch (parseError) {
				console.log('🔍 tryDirectUpdateStorage - 解析失败，使用空对象:', parseError);
				storageData = {};
			}
		} else {
			console.log('🔍 tryDirectUpdateStorage - 文件不存在，创建目录');
			// 确保目录存在
			const globalStorageDir = path.dirname(filePath);
			if (!fs.existsSync(globalStorageDir)) {
				fs.mkdirSync(globalStorageDir, { recursive: true });
				console.log('🔍 tryDirectUpdateStorage - 目录已创建:', globalStorageDir);
			}
		}

		// 合并新数据
		console.log('🔍 tryDirectUpdateStorage - 合并前数据键:', Object.keys(storageData));
		Object.assign(storageData, newData);
		console.log('🔍 tryDirectUpdateStorage - 合并后数据键:', Object.keys(storageData));

		// 写入文件
		const finalContent = JSON.stringify(storageData, null, 2);
		fs.writeFileSync(filePath, finalContent, 'utf8');
		console.log('🔍 tryDirectUpdateStorage - 文件已写入，内容长度:', finalContent.length);

		// 验证写入是否成功
		try {
			const verifyContent = fs.readFileSync(filePath, 'utf8');
			const verifyData = JSON.parse(verifyContent);
			console.log('🔍 验证更新结果:');
			console.log('  - storage.serviceMachineId:', verifyData['storage.serviceMachineId']?.substring(0, 16) + '...');
			console.log('  - telemetry.devDeviceId:', verifyData['telemetry.devDeviceId']);
			console.log('  - telemetry.macMachineId:', verifyData['telemetry.macMachineId']?.substring(0, 16) + '...');
			console.log('  - telemetry.machineId:', verifyData['telemetry.machineId']?.substring(0, 16) + '...');
			console.log('  - augment.testUpdate:', verifyData['augment.testUpdate']);
			console.log('  - augment.updateCount:', verifyData['augment.updateCount']);
		} catch (verifyError) {
			console.log('❌ 验证失败:', verifyError);
		}

		console.log('✅ tryDirectUpdateStorage - 更新完成');
	}

	// 方法2: 修改权限后更新
	private async tryChmodAndUpdateStorage(filePath: string, newData: any): Promise<void> {
		if (fs.existsSync(filePath)) {
			fs.chmodSync(filePath, 0o666);
		}
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 方法3: 删除后重新创建
	private async tryDeleteAndUpdateStorage(filePath: string, newData: any): Promise<void> {
		if (fs.existsSync(filePath)) {
			fs.unlinkSync(filePath);
		}
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 方法4: 使用原生脚本强制更新
	private async tryNativeScriptUpdateStorage(filePath: string, newData: any): Promise<void> {
		const execAsync = promisify(exec);

		if (fs.existsSync(filePath)) {
			try {
				// 先尝试普通命令
				try {
					fs.chmodSync(filePath, 0o666);
					fs.unlinkSync(filePath);
				} catch (normalError) {
					// 普通命令失败，请求管理员权限
					const password = await this.getAdminPassword();

					if (!password) {
						throw new Error('需要管理员权限才能修改存储配置文件');
					}

					// 使用管理员权限删除文件
					if (process.platform === 'darwin' || process.platform === 'linux') {
						await execAsync(`echo "${password}" | sudo -S rm -f "${filePath}"`);
					} else if (process.platform === 'win32') {
						// Windows使用PowerShell以管理员身份运行
						const psCommand = `Remove-Item -Path "${filePath}" -Force`;
						await execAsync(`powershell -Command "Start-Process powershell -ArgumentList '-Command', '${psCommand}' -Verb RunAs -Wait"`);
					}
				}
			} catch (deleteError) {
				throw new Error(`删除存储配置文件失败: ${deleteError}`);
			}
		}

		// 重新创建文件
		await this.tryDirectUpdateStorage(filePath, newData);
	}

	// 处理API请求
	private async makeApiRequest(url: string, options: any): Promise<any> {
		console.log('🔍 makeApiRequest 开始:', { url, method: options.method, headers: options.headers });

		const https = require('https');
		const http = require('http');
		const urlModule = require('url');

		return new Promise((resolve, reject) => {
			const parsedUrl = urlModule.parse(url);
			const isHttps = parsedUrl.protocol === 'https:';
			const client = isHttps ? https : http;

			console.log('🔍 解析URL:', { hostname: parsedUrl.hostname, port: parsedUrl.port, path: parsedUrl.path, protocol: parsedUrl.protocol });

			const requestOptions = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (isHttps ? 443 : 80),
				path: parsedUrl.path,
				method: options.method || 'GET',
				headers: options.headers || {},
				timeout: 15000
			};

			const req = client.request(requestOptions, (res: any) => {
				let data = '';
				res.on('data', (chunk: any) => {
					data += chunk;
				});

				res.on('end', () => {
					try {
						console.log('🔍 收到响应:', { status: res.statusCode, statusText: res.statusMessage, dataLength: data.length });
						console.log('🔍 响应数据前100字符:', data.substring(0, 100));

						const result = {
							status: res.statusCode,
							statusText: res.statusMessage,
							headers: res.headers,
							data: data
						};

						if (res.statusCode >= 200 && res.statusCode < 300) {
							console.log('✅ 请求成功');
							resolve(result);
						} else {
							console.log('❌ 请求失败:', res.statusCode, res.statusMessage);
							console.log('❌ 错误响应数据:', data);
							// 创建包含响应数据的错误对象，传递实际的JSON数据而不是状态消息
							const error = new Error(`HTTP ${res.statusCode}: ${data}`);
							(error as any).response = result;
							reject(error);
						}
					} catch (error) {
						console.log('❌ 解析响应失败:', error);
						reject(error instanceof Error ? error : new Error(String(error)));
					}
				});
			});

			req.on('error', (error: any) => {
				console.log('❌ 请求错误:', error);
				reject(error);
			});

			req.on('timeout', () => {
				console.log('❌ 请求超时');
				req.destroy();
				reject(new Error('Request timeout'));
			});

			if (options.body) {
				req.write(options.body);
			}

			req.end();
		});
	}

	public dispose() {
		TempMailPanel.currentPanel = undefined;

		// 清理资源
		this._panel.dispose();

		while (this._disposables.length) {
			const x = this._disposables.pop();
			if (x) {
				x.dispose();
			}
		}
	}

	private _update() {
		const webview = this._panel.webview;
		this._panel.webview.html = this._getHtmlForWebview(webview);
	}

	private _getHtmlForWebview(_webview: vscode.Webview) {
		const nonce = getNonce();
		return this.getHtmlContent(nonce);
	}

	// 静态方法：生成HTML内容，供侧边栏复用
	public static generateHtmlContent(nonce: string): string {
		const cspContent = `default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}' 'unsafe-inline'; img-src http://images.yungouos.com https://images.yungouos.com https: data: blob:; connect-src http://************:8080 https://************:8080 http://localhost:8080 https://localhost:8080;`;

		return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="${cspContent}">
    <title>临时邮箱插件</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #0d1117 0%, #161b22 50%, #21262d 100%);
            min-height: 100vh;
            color: #f0f6fc;
            line-height: 1.6;
        }
        .container {
            max-width: 480px;
            margin: 0 auto;
            min-height: 100vh;
            background: #0d1117;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
            position: relative;
            border: 1px solid #30363d;
        }
        .page { display: none; min-height: 100vh; }
        .page.active { display: block; }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #161b22;
            border-bottom: 1px solid #30363d;
        }
        .app-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .app-title h1 {
            font-size: 18px;
            font-weight: 600;
            color: #f0f6fc;
            margin: 0;
        }
        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #238636;
            color: white;
            width: 100%;
            margin: 16px 0;
        }
        .btn-primary:hover { background: #2ea043; }
        .btn-secondary {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
        }
        .btn-secondary:hover { background: #30363d; }
        .btn-copy {
            background: #58a6ff;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
        }
        .btn-copy:hover { background: #388bfd; }
        .btn-copy:disabled {
            background: #484f58;
            cursor: not-allowed;
        }
        .btn-primary:disabled {
            background: #484f58;
            cursor: not-allowed;
            opacity: 0.6;
        }
        .btn-countdown {
            background: #fd7e14 !important;
            color: white !important;
            cursor: not-allowed !important;
        }
        #refresh-codes-btn {
            background: none !important;
            border: none !important;
            padding: 4px !important;
            font-size: 18px;
            cursor: pointer;
            color: #8b949e;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: auto;
            transition: all 0.2s ease;
        }
        #refresh-codes-btn:hover {
            color: #58a6ff;
            transform: rotate(180deg);
            background: none !important;
        }
        #refresh-codes-btn:active {
            transform: rotate(180deg) scale(0.95);
        }
        .btn-login {
            background: #238636;
            color: white;
            padding: 6px 16px;
            border-radius: 6px;
        }
        .main-content {
            padding: 20px;
            background: #0d1117;
            min-height: calc(100vh - 80px);
        }
        .card {
            background: rgba(22, 27, 34, 0.8);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            border: 1px solid #30363d;
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #f0f6fc;
        }
        .email-display {
            background: rgba(13, 17, 23, 0.6);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #30363d;
            margin-bottom: 12px;
        }
        .email-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .email-text {
            flex: 1;
            font-family: monospace;
            font-size: 14px;
            color: #f0f6fc;
            word-break: break-all;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        .action-buttons .btn {
            flex: 1;
            margin: 0;
        }
        .verification-code-single {
            min-height: 48px;
        }
        .code-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: rgba(13, 17, 23, 0.6);
            border-radius: 12px;
            border: 1px solid #30363d;
            border-left: 4px solid #58a6ff;
        }
        .code-content {
            flex: 1;
        }
        .code-text {
            font-family: monospace;
            font-size: 18px;
            font-weight: 700;
            color: #58a6ff;
            margin-bottom: 3px;
            letter-spacing: 2px;
        }
        .code-time {
            font-size: 12px;
            color: #8b949e;
        }
        .empty-state {
            text-align: center;
            padding: 20px;
            color: #6e7681;
        }
        .empty-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .announcement {
            background: rgba(88, 166, 255, 0.1);
            border-left: 4px solid #58a6ff;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(88, 166, 255, 0.2);
        }
        .announcement p {
            margin-bottom: 8px;
            color: #f0f6fc;
        }
        .announcement p:last-child {
            margin-bottom: 0;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #238636;
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            z-index: 1000;
            display: none;
        }
        .toast.show { display: block; }
        .toast.error { background: #da3633; }
        .toast.info { background: #58a6ff; }

        /* 认证页面样式 */
        .auth-container {
            padding: 40px 40px;
            text-align: center;
        }

        .logo {
            margin-bottom: 40px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 600;
            color: #f0f6fc;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .auth-form h2 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 32px;
            color: #f0f6fc;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .input-group input {
            width: 100%;
            padding: 16px;
            border: 1px solid #30363d;
            border-radius: 12px;
            font-size: 16px;
            background: #21262d;
            color: #f0f6fc;
        }

        .input-group input:focus {
            outline: none;
            border-color: #58a6ff;
            background: #161b22;
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.15);
        }

        .input-group input::placeholder {
            color: #8b949e;
        }

        .switch-text {
            color: #8b949e;
            font-size: 14px;
            margin-top: 20px;
        }

        .switch-text a {
            color: #58a6ff;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
        }

        .switch-text a:hover {
            text-decoration: underline;
        }

        /* 邮箱输入组合样式 */
        .input-label {
            display: block;
            color: #f0f6fc;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .email-input-group {
            display: flex;
            align-items: center;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 0;
            transition: border-color 0.2s;
        }

        .email-input-group:focus-within {
            border-color: #58a6ff;
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        }

        .email-input-group input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 12px 16px;
            color: #f0f6fc;
            font-size: 14px;
            outline: none;
        }

        .email-separator {
            color: #7d8590;
            font-size: 16px;
            font-weight: 500;
            padding: 0 8px;
            user-select: none;
        }

        .domain-select {
            background: transparent;
            border: none;
            color: #f0f6fc;
            font-size: 14px;
            padding: 12px 16px;
            outline: none;
            cursor: pointer;
            min-width: 140px;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237d8590' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        .domain-select option {
            background: #21262d;
            color: #f0f6fc;
            padding: 8px;
        }

        .verification-section {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .verification-section .input-group {
            flex: 1;
            margin-bottom: 0;
        }

        /* 个人中心图标容器 */
        .profile-icon-container {
            position: relative;
            display: inline-block;
        }

        /* VIP徽章V样式 */
        .vip-badge-v {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 16px;
            height: 16px;
            background: #ffd700;
            color: #1a1a1a;
            border-radius: 50%;
            font-size: 10px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #161b22;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
            display: none;
        }

        .btn-icon {
            background: transparent;
            border: 1px solid #30363d;
            padding: 8px;
            border-radius: 8px;
            font-size: 16px;
            width: 40px;
            height: 40px;
            color: #f0f6fc;
        }

        .btn-icon:hover {
            background: #21262d;
            border-color: #484f58;
        }

        .btn-back {
            background: transparent;
            color: #58a6ff;
            padding: 8px 16px;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }

        .btn-back:hover {
            background: rgba(88, 166, 255, 0.1);
        }

        .btn-danger {
            background: #da3633;
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(218, 54, 51, 0.3);
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-tutorial {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .btn-tutorial:hover {
            background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        /* 个人中心样式 */
        .profile-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: #161b22;
            border-bottom: 1px solid #30363d;
            backdrop-filter: blur(20px);
        }

        .profile-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #f0f6fc;
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }

        .btn-upgrade {
            background: #6366f1;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 8px;
            border: none;
        }

        .btn-upgrade:hover {
            background: #5b21b6;
        }

        .profile-content {
            padding: 20px;
            background: #0d1117;
            min-height: calc(100vh - 80px);
        }

        /* 用户信息卡片 */
        .user-info-card {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            background: #30363d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #7d8590;
        }

        .user-info-details h3 {
            margin: 0 0 8px 0;
            color: #f0f6fc;
            font-size: 18px;
            font-weight: 600;
        }

        .vip-expiry {
            margin: 0;
            color: #7d8590;
            font-size: 14px;
        }

        /* 菜单部分 */
        .menu-section {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #30363d;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: #21262d;
        }

        .menu-item span:first-child {
            color: #f0f6fc;
            font-size: 16px;
        }

        .menu-arrow {
            color: #7d8590;
            font-size: 14px;
            padding: 4px 12px;
            background: #30363d;
            border-radius: 6px;
        }

        /* 版本信息 */
        .version-section {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .version-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
        }

        .version-item span:first-child {
            color: #f0f6fc;
            font-size: 16px;
        }

        .version-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .version-info span {
            color: #7d8590;
            font-size: 14px;
        }

        .btn-check-update {
            background: #30363d;
            color: #7d8590;
            padding: 4px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
        }

        .btn-check-update:hover {
            background: #484f58;
        }

        /* 退出登录 */
        .logout-section {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
        }

        .logout-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            cursor: pointer;
        }

        .logout-item span {
            color: #f0f6fc;
            font-size: 16px;
        }

        .btn-logout {
            background: #da3633;
            color: white;
            padding: 6px 16px;
            font-size: 14px;
            border-radius: 6px;
            border: none;
        }

        .btn-logout:hover {
            background: #b91c1c;
        }

        /* VIP套餐页面样式 */
        .vip-plans-content {
            padding: 20px;
            background: #0d1117;
            min-height: calc(100vh - 80px);
        }

        .plans-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .plan-card {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 20px;
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .plan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .plan-card.featured {
            border-color: #6366f1;
            box-shadow: 0 0 0 1px #6366f1;
        }

        .plan-badge {
            position: absolute;
            top: -8px;
            right: 20px;
            background: #6366f1;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .plan-card h3 {
            color: #f0f6fc;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 12px 0;
        }

        .plan-price {
            display: flex;
            align-items: baseline;
            gap: 8px;
            margin-bottom: 6px;
        }

        .current-price {
            color: #6366f1;
            font-size: 32px;
            font-weight: 700;
        }

        .original-price {
            color: #7d8590;
            font-size: 16px;
            text-decoration: line-through;
        }

        .plan-duration {
            color: #7d8590;
            font-size: 14px;
            margin-bottom: 6px;
        }

        .plan-discount {
            color: #238636;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }

        .plan-features {
            margin-bottom: 20px;
        }

        .feature-item {
            color: #7d8590;
            font-size: 14px;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-item:before {
            content: "✓";
            color: #238636;
            font-weight: bold;
        }

        .btn-select-plan {
            width: 100%;
            background: #6366f1;
            color: white;
            padding: 12px;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-select-plan:hover {
            background: #5b21b6;
        }

        /* 温馨提示 */
        .tips-section {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .tips-icon {
            width: 40px;
            height: 40px;
            background: #238636;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .tips-content h4 {
            color: #f0f6fc;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 4px 0;
        }

        .tips-content p {
            color: #7d8590;
            font-size: 14px;
            margin: 0;
        }

        /* 支付页面样式 */
        .payment-content {
            padding: 20px;
            background: #0d1117;
            min-height: calc(100vh - 80px);
        }

        .order-info {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #30363d;
            color: #f0f6fc;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .order-item span:first-child {
            color: #7d8590;
        }

        .order-price {
            color: #6366f1;
            font-size: 18px;
            font-weight: 700;
        }

        .payment-instruction {
            text-align: center;
            margin-bottom: 20px;
        }

        .payment-instruction p {
            color: #7d8590;
            font-size: 16px;
            margin: 0;
        }

        .qr-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            background: white;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .qr-placeholder {
            color: #666;
            font-size: 14px;
        }

        .qr-timer {
            color: #7d8590;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .payment-notice {
            text-align: center;
            margin-bottom: 30px;
        }

        .payment-notice p {
            color: #7d8590;
            font-size: 14px;
            margin: 0;
        }

        .payment-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .payment-actions .btn {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .payment-actions .btn-primary {
            background: #6366f1;
            color: white;
        }

        .payment-actions .btn-primary:hover {
            background: #5b21b6;
        }

        .payment-actions .btn-secondary {
            background: #30363d;
            color: #7d8590;
            border: 1px solid #484f58;
        }

        .payment-actions .btn-secondary:hover {
            background: #484f58;
            color: #f0f6fc;
        }

        /* 联系我弹框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 12px;
            width: 90%;
            max-width: 480px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.4);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #21262d;
        }

        .modal-header h3 {
            margin: 0;
            color: #f0f6fc;
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            color: #8b949e;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            padding: 4px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .close:hover {
            color: #f0f6fc;
            background: #21262d;
        }

        .modal-body {
            padding: 24px;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 20px;
            background: #161b22;
            border: 1px solid #21262d;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .contact-item:hover {
            border-color: #30363d;
            background: #1c2128;
        }

        .contact-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #21262d;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .contact-details {
            flex: 1;
        }

        .contact-details h4 {
            margin: 0 0 8px 0;
            color: #f0f6fc;
            font-size: 16px;
            font-weight: 600;
        }

        .wechat-info, .email-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .wechat-id, .email-id {
            color: #58a6ff;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            font-weight: 500;
            background: #0d1117;
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid #21262d;
            flex: 1;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            height: auto;
            min-height: auto;
        }

        .contact-desc {
            margin: 0;
            color: #8b949e;
            font-size: 13px;
            line-height: 1.4;
        }

        /* 介绍弹框样式 */
        .introduction-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .intro-section {
            padding: 16px;
            background: #161b22;
            border: 1px solid #21262d;
            border-radius: 8px;
        }

        .intro-section h4 {
            margin: 0 0 12px 0;
            color: #f0f6fc;
            font-size: 16px;
            font-weight: 600;
        }

        .intro-section p {
            margin: 0;
            color: #c9d1d9;
            font-size: 14px;
            line-height: 1.6;
        }

        .intro-section ul {
            margin: 0;
            padding-left: 20px;
            color: #c9d1d9;
            font-size: 14px;
            line-height: 1.8;
        }

        .intro-section li {
            margin-bottom: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .modal-content {
                width: 95%;
                margin: 20px;
            }

            .modal-header, .modal-body {
                padding: 16px;
            }

            .contact-item {
                padding: 16px;
            }

            .wechat-info, .email-info {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录/注册页面 -->
        <div id="auth-page" class="page">
            <div class="auth-container">
                <div class="logo">
                    <h1>Augment Star</h1>
                </div>

                <!-- 登录表单 -->
                <div id="login-form" class="auth-form active">
                    <h2>登录</h2>
                    <div class="input-group">
                        <input type="email" id="login-email" placeholder="邮箱地址" required>
                    </div>
                    <div class="input-group">
                        <input type="password" id="login-password" placeholder="密码" required>
                    </div>
                    <button class="btn btn-primary" id="login-submit-btn">登录</button>
                    <p class="switch-text">还没有账号？<a id="show-register-link">立即注册</a></p>
                </div>

                <!-- 注册表单 -->
                <div id="register-form" class="auth-form">
                    <h2>注册</h2>
                    <div class="input-group">
                        <label class="input-label">邮箱地址</label>
                        <div class="email-input-group">
                            <input type="text" id="register-username" placeholder="用户名" required>
                            <span class="email-separator">@</span>
                            <select id="email-domain" class="domain-select" required>
                                <option value="">选择域名</option>
                                <option value="gmail.com">gmail.com</option>
                                <option value="outlook.com">outlook.com</option>
                                <option value="yahoo.com">yahoo.com</option>
                                <option value="hotmail.com">hotmail.com</option>
                                <option value="qq.com">qq.com</option>
                                <option value="163.com">163.com</option>
                                <option value="126.com">126.com</option>
                                <option value="sina.com">sina.com</option>
                            </select>
                        </div>
                    </div>
                    <div class="input-group">
                        <input type="password" id="register-password" placeholder="密码" required>
                    </div>
                    <div class="input-group">
                        <input type="password" id="register-confirm" placeholder="确认密码" required>
                    </div>
                    <div class="verification-section">
                        <div class="input-group">
                            <input type="text" id="verification-code" placeholder="验证码" required>
                        </div>
                        <button class="btn btn-secondary" id="send-verification-btn">发送验证码</button>
                    </div>
                    <button class="btn btn-primary" id="register-submit-btn">注册</button>
                    <p class="switch-text">已有账号？<a id="show-login-link">立即登录</a></p>
                </div>

                <button class="btn btn-back" id="back-to-main-btn">← 返回主页</button>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" id="profile-back-btn">← 返回</button>
                <h2>个人信息</h2>
                <button class="btn btn-upgrade" id="upgrade-btn">续费</button>
            </div>

            <div class="profile-content">
                <!-- 用户信息卡片 -->
                <div class="user-info-card">
                    <div class="user-avatar">👤</div>
                    <div class="user-info-details">
                        <h3 id="profile-email">加载中...</h3>
                        <p class="vip-expiry">加载中...</p>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="menu-section">
                    <div class="menu-item" id="introduction-item">
                        <span>介绍</span>
                        <span class="menu-arrow">查看</span>
                    </div>
                    <div class="menu-item" id="contact-item">
                        <span>联系我</span>
                        <span class="menu-arrow">查看</span>
                    </div>
                </div>

                <!-- 退出登录 -->
                <div class="logout-section">
                    <div class="logout-item" id="logout-item">
                        <span>退出登录</span>
                        <button class="btn btn-logout" id="logout-btn">退出</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP套餐选择页面 -->
        <div id="vip-plans-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" id="vip-plans-back-btn">← 返回</button>
                <h2>选择VIP套餐</h2>
            </div>

            <div class="vip-plans-content">
                <div class="plans-container">
                    <!-- 套餐将通过API动态加载 -->
                    <div class="loading-placeholder">
                        <p>正在加载套餐信息...</p>
                    </div>
                </div>

                <!-- 温馨提示 -->
                <div class="tips-section">
                    <div class="tips-icon">✓</div>
                    <div class="tips-content">
                        <h4>温馨提示</h4>
                        <p>有任何问题，随时联系我们</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付页面 -->
        <div id="payment-page" class="page">
            <div class="profile-header">
                <button class="btn btn-back" id="payment-back-btn">← 返回</button>
                <h2>订单支付</h2>
            </div>

            <div class="payment-content">
                <!-- 订单信息 -->
                <div class="order-info">
                    <div class="order-item">
                        <span>商品名称</span>
                        <span id="order-product">Augment Star 周卡无限会员</span>
                    </div>
                    <div class="order-item">
                        <span>支付金额</span>
                        <span class="order-price" id="order-amount">¥9.9</span>
                    </div>
                    <div class="order-item">
                        <span>订单编号</span>
                        <span id="order-number">202508021808302820006</span>
                    </div>
                </div>

                <!-- 支付说明 -->
                <div class="payment-instruction">
                    <p>请使用支付宝扫描下方二维码完成支付</p>
                </div>

                <!-- 二维码 -->
                <div class="qr-section">
                    <div class="qr-code">
                        <div class="qr-placeholder">二维码</div>
                    </div>
                    <div class="qr-timer">二维码有效期: <span id="countdown">4:55</span></div>
                </div>

                <!-- 支付提示 -->
                <div class="payment-notice">
                    <p>支付完成后系统将自动为您开通会员</p>
                    <p style="font-size: 12px; color: #7d8590; margin-top: 8px;">
                        💡 系统每10秒自动检测支付状态，支付成功后会自动跳转
                    </p>
                </div>

                <!-- 操作按钮 -->
                <div class="payment-actions">
                    <button class="btn btn-primary" id="confirm-payment-btn">我已完成支付</button>
                    <button class="btn btn-secondary" id="cancel-payment-btn">取消支付</button>
                </div>
            </div>
        </div>

        <div id="main-page" class="page active">
            <div class="header">
                <div class="app-title">
                    <h1>Augment Star</h1>
                </div>
                <div class="header-actions">
                    <!-- 未登录状态 -->
                    <div class="login-prompt" id="login-prompt">
                        <button class="btn btn-login" id="login-btn">登录</button>
                    </div>
                    <!-- 已登录状态 -->
                    <div class="profile-icon-container" id="profile-container" style="display: none;">
                        <button class="btn btn-icon" id="profile-btn" title="个人中心">👤</button>
                        <div class="vip-badge-v" id="vip-badge" title="VIP会员" style="display: none;">V</div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="card">
                    <div class="card-header">
                        <h3>邮箱地址</h3>
                    </div>
                    <div class="email-display">
                        <div class="email-item">
                            <span class="email-text" id="current-email">点击②  获取邮箱按钮生成邮箱地址</span>
                            <button class="btn btn-copy" id="copy-email-btn" disabled>复制</button>
                        </div>
                    </div>
                    <div class="important-notice" style="margin: 12px 0; padding: 8px; background-color: #1c2128; border-radius: 6px; color: #f85149; font-size: 13px; text-align: left;">
                        ‼️重要提示: 每次换号必须先点击重置插件,否则会被封号；请提前保存文件
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-danger" id="reset-plugin-btn">①  重置插件</button>
                        <button class="btn btn-primary" id="get-email-btn">②  获取邮箱</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>验证码</h3>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary" id="email-verification-btn" disabled>已发送验证码</button>
                            <button class="btn btn-secondary" id="refresh-codes-btn" title="刷新验证码">🔄</button>
                        </div>
                    </div>
                    <div class="verification-code-single" id="verification-code-single">
                        <div class="empty-state">
                            <div class="empty-icon">📭</div>
                            <p>暂无验证码</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>系统公告</h3>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-tutorial" id="tutorial-btn">
                                <span class="tutorial-icon">📚</span>
                                在线教程
                            </button>
                        </div>
                    </div>
                    <div class="announcement" id="announcement">
                        <p>欢迎使用临时邮箱插件！</p>
                        <p>VIP用户享有更长的邮箱有效期和更多功能。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 介绍弹框 -->
    <div id="introduction-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>关于 Augment Star</h3>
                <span class="close" id="introduction-modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="introduction-content">
                    <div class="intro-section">
                        <h4>🚀 关于 Augment Star</h4>
                        <p>Augment Star 提供专业的 Augment 插件授权账户服务，为开发者打造纯净、安全的代码开发环境。我们致力于让您丝滑使用 Augment 插件进行高效的代码开发工作。</p>
                    </div>
                    <div class="intro-section">
                        <h4>✨ 核心优势</h4>
                        <ul>
                            <li>🔐 <strong>专业授权</strong> - 提供正版 Augment 插件授权账户</li>
                            <li>🚫 <strong>防封保障</strong> - 专业的防封号策略和技术支持</li>
                            <li>⚡ <strong>丝滑体验</strong> - 稳定流畅的插件使用体验</li>
                            <li>💎 <strong>VIP服务</strong> - 会员享有优先支持和更多特权</li>
                        </ul>
                    </div>
                    <div class="intro-section">
                        <h4>🎯 适用场景</h4>
                        <p>适用于需要使用 Augment 插件进行代码开发的个人开发者、团队和企业。无论是日常编程、项目开发还是学习研究，我们都能为您提供稳定可靠的 Augment 插件使用环境。</p>
                    </div>
                    <div class="intro-section">
                        <h4>🔧 技术支持</h4>
                        <p>我们提供专业的技术支持服务，包括账户配置指导、使用问题解答、环境优化建议等。让您专注于代码开发，无需担心插件使用问题。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系我弹框 -->
    <div id="contact-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>联系客服</h3>
                <span class="close" id="contact-modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">💬</div>
                        <div class="contact-details">
                            <h4>客服微信</h4>
                            <div class="wechat-info">
                                <span class="wechat-id" id="wechat-id">加载中...</span>
                                <button class="btn btn-copy btn-small" id="copy-wechat-btn">复制</button>
                            </div>
                            <p class="contact-desc">添加微信获取技术支持和帮助</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="toast" class="toast"></div>

    <script nonce="${nonce}">
        let currentEmail = null;
        let currentVerificationCode = null;
        let currentUser = null;
        let authToken = null;
        let refreshToken = null;
        let allowedEmailDomains = ['tempmail.com']; // 默认域名，将从API获取

        // 邮箱池相关状态
        let isPolling = false; // 是否正在轮询
        let pollingInterval = null; // 轮询定时器
        let countdownInterval = null; // 倒计时定时器
        let pollingStartTime = null; // 轮询开始时间
        let buttonCooldownTime = null; // 按钮冷却结束时间

        // 全局VSCode API实例
        let vscodeApi = null;
        try {
            if (typeof acquireVsCodeApi !== 'undefined') {
                vscodeApi = acquireVsCodeApi();
            }
        } catch (error) {
        }

        // 通用错误解析函数
        function parseErrorMessage(error) {
            if (!error || !error.message) {
                return '未知错误';
            }

            try {
                // 尝试从HTTP错误消息中提取JSON部分
                const match = error.message.match(/HTTP \\d+: (.+)/);
                if (match && match[1]) {
                    const jsonStr = match[1];
                    const errorData = JSON.parse(jsonStr);
                    if (errorData.message) {
                        return errorData.message;
                    }
                }
                return error.message;
            } catch (parseError) {
                // 如果解析失败，使用原始错误消息
                return error.message;
            }
        }

        // API配置
        const CONFIG = {
            PRODUCTION: {
                baseUrl: 'http://************:8080/cursor-api',
                wsUrl: 'ws://************:8080/cursor-api/ws'
            },
            DEVELOPMENT: {
                baseUrl: 'http://localhost:8080/cursor-api',
                wsUrl: 'ws://localhost:8080/cursor-api/ws'
            }
        };

        // 当前环境配置 - 使用生产环境
        const CURRENT_ENV = 'PRODUCTION'; // 使用生产服务器
        const API_BASE_URL = CONFIG[CURRENT_ENV].baseUrl;
        const WS_URL = CONFIG[CURRENT_ENV].wsUrl;

        // 超时配置
        const CONNECT_TIMEOUT = 15000; // 15秒连接超时
        const RECEIVE_TIMEOUT = 15000; // 15秒接收超时

        // 用户限制配置
        const FREE_USER_TOTAL_REQUESTS = 6; // 免费用户总请求数
        const VIP_USER_DAILY_REQUESTS = 3; // VIP用户每日请求数

        // API服务类
        class ApiService {
            constructor() {
                this.baseURL = API_BASE_URL;
                this.token = authToken;
            }

            // 设置认证token
            setToken(token) {
                this.token = token;
                authToken = token;
            }

            // 获取请求头
            getHeaders(includeAuth = true) {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                };

                if (includeAuth && this.token) {
                    headers['Authorization'] = 'Bearer ' + this.token;
                }

                return headers;
            }

            // 通用请求方法
            async request(endpoint, options = {}) {
                const url = this.baseURL + endpoint;
                const config = {
                    method: 'GET',
                    headers: this.getHeaders(options.auth !== false),
                    timeout: CONNECT_TIMEOUT,
                    ...options
                };

                try {
                    // 使用VSCode扩展主机进行网络请求
                    return await this.makeVSCodeRequest(url, config);
                } catch (error) {
                    // 如果是401错误且有refreshToken，尝试刷新token
                    if (error.message.includes('HTTP 401') && refreshToken && options.auth !== false && !options._isRetry) {
                        try {
                            await this.refreshToken();
                            // 刷新成功，重试请求
                            const retryConfig = { ...config, _isRetry: true };
                            retryConfig.headers = this.getHeaders(true); // 使用新的token
                            return await this.makeVSCodeRequest(url, retryConfig);
                        } catch (refreshError) {
                            // Token刷新失败，清除认证信息
                            this.setToken(null);
                            authToken = null;
                            refreshToken = null;
                            currentUser = null;
                            updateLoginStatus(false);
                        }
                    }

                    this.logToConsole('error', 'API请求错误:', {
                        error: error,
                        message: error.message,
                        url: url,
                        config: config
                    });

                    if (error.message.includes('timeout')) {
                        throw new Error('请求超时，请检查网络连接');
                    } else if (error.message.includes('Failed to fetch') || error.message.includes('ECONNREFUSED')) {
                        throw new Error('网络连接失败，请检查服务器状态');
                    } else if (error.message.includes('ENOTFOUND')) {
                        throw new Error('无法解析服务器地址，请检查网络设置');
                    }

                    throw error;
                }
            }

            // 使用VSCode扩展主机进行网络请求
            async makeVSCodeRequest(url, config) {
                return new Promise((resolve, reject) => {
                    const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                    // 监听响应
                    const responseHandler = (event) => {
                        const message = event.data;
                        if (message.command === 'apiResponse' && message.requestId === requestId) {
                            window.removeEventListener('message', responseHandler);

                            if (message.success) {
                                // 现在message.data已经是解析后的JSON数据，直接使用
                                const data = message.data;
                                console.log('🔍 成功响应 - 解析后的数据:', data);
                                resolve(data);
                            } else {
                                // 如果错误消息包含响应数据，使用响应数据处理错误
                                if (message.response) {
                                    let errorMessage = this.getErrorMessage(message.response.status, message.response.data);
                                    reject(new Error(errorMessage));
                                } else {
                                    reject(new Error(message.error));
                                }
                            }
                        }
                    };

                    window.addEventListener('message', responseHandler);

                    // 发送请求到VSCode扩展主机
                    if (vscodeApi) {
                        vscodeApi.postMessage({
                            command: 'apiRequest',
                            requestId: requestId,
                            url: url,
                            options: config
                        });
                    } else {
                        window.removeEventListener('message', responseHandler);
                        reject(new Error('VSCode API不可用'));
                    }

                    // 设置超时
                    setTimeout(() => {
                        window.removeEventListener('message', responseHandler);
                        reject(new Error('请求超时'));
                    }, RECEIVE_TIMEOUT);
                });
            }

            // 获取错误信息
            getErrorMessage(status, data) {
                let errorMessage = '请求失败';

                // 首先尝试解析服务端返回的自定义错误信息
                try {
                    const parsedData = JSON.parse(data);
                    if (parsedData && parsedData.message) {
                        errorMessage = parsedData.message;

                        // 如果是401错误且包含认证相关信息，清除本地认证信息
                        if (status === 401 && (
                            parsedData.message.includes('登录已过期') ||
                            parsedData.message.includes('token') ||
                            parsedData.message.includes('认证')
                        )) {
                            this.setToken(null);
                            authToken = null;
                            refreshToken = null;
                            currentUser = null;
                            updateLoginStatus(false);
                        }

                        return errorMessage;
                    }
                } catch (e) {
                    // 如果解析失败，继续使用HTTP状态码的默认处理
                }

                // 如果没有自定义错误信息，使用HTTP状态码的默认处理
                if (status === 401) {
                    errorMessage = '登录已过期，请重新登录';
                    // 清除本地认证信息
                    this.setToken(null);
                    authToken = null;
                    refreshToken = null;
                    currentUser = null;
                    updateLoginStatus(false);
                } else if (status === 403) {
                    errorMessage = '权限不足或操作被禁止';
                } else if (status === 404) {
                    errorMessage = '请求的资源不存在';
                } else if (status === 409) {
                    errorMessage = '资源冲突，可能是邮箱已被注册';
                } else if (status === 429) {
                    errorMessage = '请求过于频繁，请稍后再试';
                } else if (status === 500) {
                    errorMessage = '服务器内部错误，请稍后再试';
                } else if (status === 502) {
                    errorMessage = '服务器网关错误，请稍后再试';
                } else if (status === 503) {
                    errorMessage = '服务暂时不可用，请稍后再试';
                } else {
                    // 最后尝试解析字符串数据
                    if (typeof data === 'string' && data.trim()) {
                        errorMessage = data;
                    }
                }

                return errorMessage;
            }

            // 发送日志到VSCode控制台
            logToConsole(level, message, data) {
                if (vscodeApi) {
                    vscodeApi.postMessage({
                        command: 'log',
                        level: level,
                        message: message,
                        data: data
                    });
                }

            }



            // GET请求
            async get(endpoint, options = {}) {
                return this.request(endpoint, { method: 'GET', ...options });
            }

            // POST请求
            async post(endpoint, data = null, options = {}) {
                const config = {
                    method: 'POST',
                    ...options
                };

                if (data) {
                    config.body = JSON.stringify(data);
                }

                return this.request(endpoint, config);
            }

            // PUT请求
            async put(endpoint, data = null, options = {}) {
                const config = {
                    method: 'PUT',
                    ...options
                };

                if (data) {
                    config.body = JSON.stringify(data);
                }

                return this.request(endpoint, config);
            }

            // DELETE请求
            async delete(endpoint, options = {}) {
                return this.request(endpoint, { method: 'DELETE', ...options });
            }

            // 用户认证相关API
            async login(email, password) {
                return this.post('/api/auth/login', { email, password }, { auth: false });
            }

            async register(email, password, verificationCode) {
                return this.post('/api/auth/register', {
                    email,
                    password,
                    verificationCode
                }, { auth: false });
            }

            async sendVerificationCode(email) {
                return this.post('/api/auth/send-code?email=' + encodeURIComponent(email), null, { auth: false });
            }

            async getUserStatus() {
                return this.get('/api/auth/status');
            }

            // VIP相关API
            async getVipPackages() {
                return this.get('/api/vip/packages');
            }

            async getVipStatus() {
                return this.get('/api/vip/status');
            }

            // 订单相关API
            async createOrder(membershipTypeId, paymentMethod = 'alipay') {
                return this.post('/api/orders', { membershipTypeId, paymentMethod });
            }

            async cancelOrder(orderNo) {
                return this.post('/api/orders/' + orderNo + '/cancel');
            }

            async refreshPaymentQrCode(orderNo) {
                return this.post('/api/payment/qrcode/' + orderNo);
            }

            async getPaymentStatus(orderNo) {
                return this.get('/api/payment/status/' + orderNo);
            }

            async getAllowedEmailDomains() {
                return this.get('/api/auth/allowed-email-domains');
            }

            // 邮箱池相关API
            async requestEmail() {
                return this.post('/api/email-pool/request');
            }

            async startVerificationCheck(emailAddress) {
                return this.post('/api/email-pool/start-check', { emailAddress });
            }

            async getEmailStatus(emailAddress) {
                return this.get('/api/email-pool/status/' + encodeURIComponent(emailAddress));
            }

            // 临时邮箱相关API（已废弃，使用邮箱池API）
            async getTempEmail() {
                // 重定向到邮箱池API
                return this.requestEmail();
            }

            async getTempEmailMessages(email) {
                // 重定向到邮箱池状态查询API
                return this.getEmailStatus(email);
            }

            // 获取FAQ教程链接
            async getFaqUrl() {
                return this.get('/api/faqs/url');
            }

            // 获取公告内容（FAQ列表）
            async getFaqs() {
                return this.get('/api/faqs');
            }

            // FAQ相关API
            async getFaqs() {
                return this.get('/api/faqs', { auth: false });
            }

            async getFaq(id) {
                return this.get('/api/faqs/' + id, { auth: false });
            }

            // 获取联系方式
            async getContactInfo() {
                return this.get('/api/contact', { auth: false });
            }

            // Token刷新
            async refreshToken() {
                if (!refreshToken) {
                    throw new Error('没有刷新令牌');
                }

                const response = await this.post('/api/auth/refresh', {
                    refreshToken: refreshToken
                }, { auth: false });

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际token数据
                const tokenData = response.data || response;

                // 更新token
                authToken = tokenData.accessToken;
                if (tokenData.refreshToken) {
                    refreshToken = tokenData.refreshToken;
                }

                this.setToken(authToken);
                saveAuthInfo();

                return response;
            }
        }

        // 创建API服务实例
        const apiService = new ApiService();





        // 加载允许的邮箱域名
        async function loadAllowedEmailDomains() {
            try {
                const response = await apiService.getAllowedEmailDomains();

                // 处理不同的响应格式
                let domains = null;

                if (response && response.data && Array.isArray(response.data)) {
                    // 标准API响应格式：{code: 200, data: ["qq.com", "163.com", ...]}
                    domains = response.data;
                } else if (response && Array.isArray(response)) {
                    // 直接返回数组
                    domains = response;
                } else if (response && typeof response === 'string') {
                    // 返回逗号分隔的字符串
                    domains = response.split(',').map(domain => domain.trim()).filter(domain => domain);
                }

                if (domains && domains.length > 0) {
                    allowedEmailDomains = domains;

                    // 更新注册页面的邮箱后缀下拉框
                    updateEmailDomainSelect(domains);
                }
            } catch (error) {
            }
        }

        // 更新注册页面的邮箱后缀下拉框
        function updateEmailDomainSelect(domains) {
            const emailDomainSelect = document.getElementById('email-domain');
            if (!emailDomainSelect) {
                return;
            }

            // 清空现有选项（保留第一个默认选项）
            emailDomainSelect.innerHTML = '<option value="">选择域名</option>';

            // 添加从API获取的域名选项
            domains.forEach(domain => {
                const option = document.createElement('option');
                option.value = domain;
                option.textContent = domain;
                emailDomainSelect.appendChild(option);
            });
        }

        // 加载系统公告
        async function loadAnnouncements() {
            try {
                const response = await apiService.getFaqs();

                // 处理API响应
                let faqs = [];
                if (response && response.data && Array.isArray(response.data)) {
                    faqs = response.data;
                } else if (response && Array.isArray(response)) {
                    faqs = response;
                }

                if (faqs.length > 0) {
                    // 过滤已发布的公告，按排序顺序排列
                    const publishedFaqs = faqs
                        .filter(faq => faq.isPublished === 1)
                        .sort((a, b) => a.sortOrder - b.sortOrder);

                    updateAnnouncementDisplay(publishedFaqs);
                } else {
                    updateAnnouncementDisplay([]);
                }
            } catch (error) {
                updateAnnouncementDisplay([]);
            }
        }

        // 更新公告显示
        function updateAnnouncementDisplay(faqs) {
            const announcementElement = document.getElementById('announcement');
            if (!announcementElement) {
                return;
            }

            if (faqs.length > 0) {
                // 显示API获取的公告
                let announcementHtml = '';

                faqs.forEach((faq, index) => {
                    announcementHtml +=
                        '<div class="faq-item" style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #21262d;">' +
                            '<h4 style="margin: 0 0 8px 0; color: #f0f6fc; font-size: 14px; font-weight: 600;">' +
                                faq.question +
                            '</h4>' +
                            '<p style="margin: 0; color: #c9d1d9; font-size: 13px; line-height: 1.5;">' +
                                faq.answer +
                            '</p>' +
                        '</div>';
                });

                announcementElement.innerHTML = announcementHtml;
            } else {
                // 显示默认公告
                announcementElement.innerHTML =
                    '<p>欢迎使用临时邮箱插件！</p>' +
                    '<p>VIP用户享有更长的邮箱有效期和更多功能。</p>';
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast ' + type;
            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // 页面切换函数
        function showPage(pageId) {
            // 如果离开支付页面，停止支付状态轮询
            if (pageId !== 'payment-page') {
                stopPaymentStatusPolling();
            }

            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(pageId).classList.add('active');
        }

        function showAuth() {
            showPage('auth-page');
        }

        function showMain() {
            showPage('main-page');
        }

        function showLogin() {
            document.getElementById('login-form').classList.add('active');
            document.getElementById('register-form').classList.remove('active');
        }

        function showRegister() {
            document.getElementById('register-form').classList.add('active');
            document.getElementById('login-form').classList.remove('active');
        }

        // 认证相关函数
        async function login() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            if (!email || !password) {
                showToast('请填写完整信息', 'error');
                return;
            }

            if (!validateEmail(email)) {
                showToast('请输入有效的邮箱地址', 'error');
                return;
            }

            showToast('登录中...', 'info');

            try {
                const response = await apiService.login(email, password);
                console.log('🔍 登录响应:', response);

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际登录数据
                const loginData = response.data || response;
                console.log('🔍 登录数据:', loginData);

                // 保存认证信息
                authToken = loginData.accessToken;
                refreshToken = loginData.refreshToken;
                apiService.setToken(authToken);

                currentUser = {
                    userId: loginData.userId,
                    email: loginData.email,
                    role: loginData.role,
                    username: loginData.username
                };

                // 获取用户详细状态
                await loadUserStatus();

                // 保存认证信息
                saveAuthInfo();

                updateLoginStatus(true, currentUser.email);

                // 立即更新个人中心信息
                updateProfileInfo();

                showToast('登录成功！');
                showMain();

            } catch (error) {
                const errorMessage = parseErrorMessage(error) || '登录失败，请检查邮箱和密码';
                showToast(errorMessage, 'error');
            }
        }

        async function register() {
            const username = document.getElementById('register-username').value;
            const domain = document.getElementById('email-domain').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm').value;
            const verificationCode = document.getElementById('verification-code').value;

            const email = username && domain ? username + '@' + domain : '';

            if (!username || !domain || !password || !confirmPassword || !verificationCode) {
                showToast('请填写完整信息', 'error');
                return;
            }

            if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
                showToast('用户名只能包含字母、数字、点号、下划线和连字符', 'error');
                return;
            }

            if (!validateEmail(email)) {
                showToast('请输入有效的邮箱地址', 'error');
                return;
            }

            if (password.length < 6) {
                showToast('密码长度至少6位', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showToast('两次密码输入不一致', 'error');
                return;
            }

            if (!/^\\d{6}$/.test(verificationCode)) {
                showToast('验证码格式不正确', 'error');
                return;
            }

            showToast('注册中...', 'info');

            try {
                const response = await apiService.register(email, password, verificationCode);

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际注册数据
                const registerData = response.data || response;

                // 保存认证信息
                authToken = registerData.accessToken;
                refreshToken = registerData.refreshToken;
                apiService.setToken(authToken);

                currentUser = {
                    userId: registerData.userId,
                    email: registerData.email,
                    role: registerData.role,
                    username: registerData.username
                };

                // 获取用户详细状态
                await loadUserStatus();

                // 保存认证信息
                saveAuthInfo();

                updateLoginStatus(true, currentUser.email);

                // 立即更新个人中心信息
                updateProfileInfo();

                showToast('注册成功！');
                showMain();

            } catch (error) {
                const parsedError = parseErrorMessage(error);
                let errorMessage = '注册失败';

                if (parsedError.includes('邮箱已被注册')) {
                    errorMessage = '该邮箱已被注册，请使用其他邮箱或直接登录';
                } else if (parsedError.includes('验证码')) {
                    errorMessage = '验证码无效或已过期，请重新获取';
                } else if (parsedError.includes('参数错误')) {
                    errorMessage = '请检查输入信息是否正确';
                } else {
                    errorMessage = parsedError || errorMessage;
                }

                showToast(errorMessage, 'error');
            }
        }

        async function sendVerificationCodeForRegister() {
            const username = document.getElementById('register-username').value;
            const domain = document.getElementById('email-domain').value;

            const email = username && domain ? username + '@' + domain : '';

            if (!username || !domain) {
                showToast('请先完整填写邮箱地址', 'error');
                return;
            }

            if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
                showToast('用户名格式不正确', 'error');
                return;
            }

            if (!validateEmail(email)) {
                showToast('请输入有效的邮箱地址', 'error');
                return;
            }

            showToast('验证码发送中...', 'info');

            try {
                await apiService.sendVerificationCode(email);
                showToast('验证码已发送到您的邮箱，请查收');

                // 启动倒计时
                startVerificationCodeCountdown();

            } catch (error) {
                let errorMessage = '发送验证码失败';

                if (error.message.includes('邮箱已被注册')) {
                    errorMessage = '该邮箱已被注册，请直接登录';
                } else if (error.message.includes('频繁')) {
                    errorMessage = '发送过于频繁，请稍后再试';
                } else if (error.message.includes('黑名单')) {
                    errorMessage = '该邮箱域名不支持注册';
                } else {
                    errorMessage = error.message || errorMessage;
                }

                showToast(errorMessage, 'error');
            }
        }

        // 辅助函数
        function validateEmail(email) {
            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
            return emailRegex.test(email);
        }

        function startVerificationCodeCountdown() {
            const sendBtn = document.getElementById('send-verification-btn');
            if (!sendBtn) return;

            let countdown = 60;
            sendBtn.disabled = true;
            sendBtn.textContent = countdown + 's后重发';

            const timer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    sendBtn.textContent = countdown + 's后重发';
                } else {
                    clearInterval(timer);
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送验证码';
                }
            }, 1000);
        }

        async function loadUserStatus() {
            if (!authToken) return;

            try {
                const response = await apiService.getUserStatus();

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际用户数据
                const userStatus = response.data || response;

                currentUser = {
                    ...currentUser,
                    userId: userStatus.userId,
                    email: userStatus.email,
                    role: userStatus.role,
                    vipLevel: userStatus.vipLevel,
                    isVip: userStatus.isVip,
                    totalCount: userStatus.totalCount,
                    maxDailyCount: userStatus.maxDailyCount,
                    todayUsedCount: userStatus.todayUsedCount,
                    vipExpiryDate: userStatus.vipExpiryDate
                };

                // 更新个人中心显示的信息
                updateProfileInfo();

                // 更新首页登录状态和VIP徽章显示
                updateLoginStatus(true, currentUser.email);

            } catch (error) {
            }
        }

        function updateProfileInfo() {
            const profileEmail = document.getElementById('profile-email');
            if (profileEmail && currentUser && currentUser.email) {
                profileEmail.textContent = currentUser.email;
            }

            // 更新VIP到期时间显示
            const vipExpiry = document.querySelector('.vip-expiry');
            if (vipExpiry && currentUser) {
                if (currentUser.isVip && currentUser.vipExpiryDate) {
                    const expiryDate = new Date(currentUser.vipExpiryDate);
                    const currentDate = new Date();
                    const formattedDate = expiryDate.toLocaleDateString('zh-CN');

                    // 比较当前时间和VIP到期时间
                    if (currentDate > expiryDate) {
                        vipExpiry.textContent = 'VIP 到期时间: ' + formattedDate + ' 已过期';
                    } else {
                        vipExpiry.textContent = 'VIP 到期时间: ' + formattedDate;
                    }
                } else if (currentUser.isVip) {
                    vipExpiry.textContent = 'VIP 用户';
                } else {
                    vipExpiry.textContent = '普通用户';
                }
            }

            // 更新续费按钮文本
            const upgradeBtn = document.getElementById('upgrade-btn');
            if (upgradeBtn && currentUser) {
                if (currentUser.isVip) {
                    // 任何VIP用户（包括过期的）都显示"续费"
                    upgradeBtn.textContent = '续费';
                } else {
                    // 普通用户显示"升级VIP"
                    upgradeBtn.textContent = '升级VIP';
                }
            }
        }

        // 检查用户使用限制
        function checkUserLimits() {
            if (!currentUser) return false;

            if (currentUser.isVip) {
                // VIP用户检查每日限制
                if (currentUser.todayUsedCount >= currentUser.maxDailyCount) {
                    showToast('今日使用次数已达上限，请明天再试', 'error');
                    return false;
                }
            } else {
                // 免费用户检查总限制
                if (currentUser.totalCount >= FREE_USER_TOTAL_REQUESTS) {
                    showToast('免费使用次数已用完，请升级VIP继续使用', 'error');
                    showUpgrade();
                    return false;
                }
            }

            return true;
        }

        function logout() {
            // 停止所有轮询和倒计时
            stopPolling();
            stopCountdown();

            currentUser = null;
            currentEmail = null;
            currentVerificationCode = null;
            authToken = null;
            refreshToken = null;
            apiService.setToken(null);

            // 清除本地存储
            clearAuthInfo();

            updateLoginStatus(false);

            document.getElementById('current-email').textContent = '点击②  获取邮箱按钮生成邮箱地址';
            document.getElementById('copy-email-btn').disabled = true;

            // 重置邮箱验证码按钮
            const emailVerificationBtn = document.getElementById('email-verification-btn');
            if (emailVerificationBtn) {
                emailVerificationBtn.disabled = true;
                emailVerificationBtn.textContent = '已发送验证码';
            }

            resetVerificationUI();

            showToast('已退出登录');
            showMain(); // 返回主页
        }

        function updateLoginStatus(isLoggedIn, email = '') {
            const loginPrompt = document.getElementById('login-prompt');
            const profileContainer = document.getElementById('profile-container');
            const vipBadge = document.getElementById('vip-badge');

            if (isLoggedIn) {
                loginPrompt.style.display = 'none';
                profileContainer.style.display = 'flex';

                // 只有当用户是有效VIP（未过期）时才显示V徽章
                const isValidVip = currentUser && currentUser.isVip && isVipValid();
                if (isValidVip) {
                    vipBadge.style.display = 'flex';
                } else {
                    vipBadge.style.display = 'none';
                }
            } else {
                loginPrompt.style.display = 'flex';
                profileContainer.style.display = 'none';
                vipBadge.style.display = 'none';
            }
        }

        // 检查VIP是否有效（未过期）
        function isVipValid() {
            if (!currentUser || !currentUser.isVip) {
                return false;
            }

            // 如果没有到期时间，认为是永久VIP
            if (!currentUser.vipExpiryDate) {
                return true;
            }

            // 检查是否过期
            const expiryDate = new Date(currentUser.vipExpiryDate);
            const currentDate = new Date();
            return currentDate <= expiryDate;
        }

        function showProfile() {
            if (!currentUser) {
                showToast('请先登录', 'error');
                showAuth();
                return;
            }

            // 更新个人中心的用户信息
            updateProfileInfo();
            showPage('profile-page');
        }

        async function openTutorial() {
            showToast('获取教程链接中...', 'info');

            try {
                // 调用API获取FAQ教程链接
                const response = await apiService.getFaqUrl();

                let tutorialUrl;

                // 处理不同的响应格式
                if (typeof response === 'string') {
                    // API直接返回字符串URL
                    tutorialUrl = response;
                } else if (response && response.data && response.data.url) {
                    // API响应格式：{code: 200, data: {url: "https://..."}}
                    tutorialUrl = response.data.url;
                } else if (response && response.data && typeof response.data === 'string') {
                    // 标准API响应格式：{code: 200, data: "https://..."}
                    tutorialUrl = response.data;
                } else {
                    throw new Error('API响应格式错误');
                }

                // 验证URL格式
                if (!tutorialUrl || !tutorialUrl.startsWith('http')) {
                    throw new Error('无效的教程链接');
                }

                // 发送消息给VSCode扩展主机来打开浏览器
                if (vscodeApi) {
                    vscodeApi.postMessage({
                        command: 'openExternal',
                        url: tutorialUrl
                    });
                }

                showToast('正在打开在线教程...', 'info');

            } catch (error) {
                // 使用默认的教程链接作为备用方案
                const defaultTutorialUrl = 'https://github.com/microsoft/vscode-extension-samples';

                showToast('获取教程链接失败，使用默认链接', 'warning');

                if (vscodeApi) {
                    vscodeApi.postMessage({
                        command: 'openExternal',
                        url: defaultTutorialUrl
                    });
                }
            }
        }

        function showIntroduction() {
            const modal = document.getElementById('introduction-modal');
            modal.classList.add('show');
        }

        async function showContact() {
            const modal = document.getElementById('contact-modal');
            modal.classList.add('show');

            // 加载联系方式信息
            await loadContactInfo();
        }

        async function loadContactInfo() {
            try {
                const response = await apiService.getContactInfo();

                // 检查响应数据结构 - API返回格式: {data: {contacts: [...]}}
                if (response && response.data && response.data.contacts && Array.isArray(response.data.contacts)) {
                    // 查找微信联系方式 - API返回的类型是 "WeChat"
                    const wechatContact = response.data.contacts.find(contact =>
                        contact.type === 'WeChat' || contact.type === 'wechat'
                    );

                    if (wechatContact && wechatContact.value) {
                        const wechatIdElement = document.getElementById('wechat-id');
                        if (wechatIdElement) {
                            wechatIdElement.textContent = wechatContact.value;
                        }
                        return;
                    }
                }

                // 如果没有找到微信联系方式，使用默认值
                const wechatIdElement = document.getElementById('wechat-id');
                if (wechatIdElement) {
                    wechatIdElement.textContent = 'augment_star_2024';
                }
            } catch (error) {
                // 使用默认值
                const wechatIdElement = document.getElementById('wechat-id');
                if (wechatIdElement) {
                    wechatIdElement.textContent = 'augment_star_2024';
                }
            }
        }

        function hideContactModal() {
            const modal = document.getElementById('contact-modal');
            modal.classList.remove('show');
        }

        function hideIntroductionModal() {
            const modal = document.getElementById('introduction-modal');
            modal.classList.remove('show');
        }

        function copyWechatId() {
            const wechatId = document.getElementById('wechat-id').textContent;
            navigator.clipboard.writeText(wechatId).then(() => {
                showToast('微信号已复制到剪贴板');
            }).catch(() => {
                showToast('复制失败', 'error');
            });
        }



        function checkUpdate() {
            showToast('检查更新中...', 'info');
            setTimeout(() => {
                showToast('当前已是最新版本！');
            }, 1500);
        }

        async function showUpgrade() {
            if (!currentUser) {
                showToast('请先登录', 'error');
                showAuth();
                return;
            }

            showToast('加载套餐信息...', 'info');
            await loadVipPackages();
            showPage('vip-plans-page');
        }

        // 显示VIP套餐页面的别名函数
        async function showVipPlans() {
            await showUpgrade();
        }

        async function loadVipPackages() {
            try {
                const response = await apiService.getVipPackages();

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际套餐数据
                const packages = response.data || response;

                updateVipPackagesDisplay(packages);
            } catch (error) {
                showToast('获取套餐信息失败', 'error');
            }
        }

        function updateVipPackagesDisplay(packages) {
            if (!packages || !Array.isArray(packages)) {
                return;
            }

            const plansContainer = document.querySelector('.plans-container');
            if (!plansContainer) {
                return;
            }

            // 清空现有内容
            plansContainer.innerHTML = '';

            // 按推荐级别和排序顺序排序
            const sortedPackages = packages
                .filter(pkg => pkg.status === 1) // 只显示启用的套餐
                .sort((a, b) => {
                    // 先按推荐级别排序，再按sortOrder排序
                    if (a.recommendLevel !== b.recommendLevel) {
                        return b.recommendLevel - a.recommendLevel;
                    }
                    return a.sortOrder - b.sortOrder;
                });

            // 动态生成套餐卡片
            sortedPackages.forEach((pkg, index) => {
                const isRecommended = pkg.recommendLevel > 1;
                const discountPercent = pkg.originalPrice > pkg.price ?
                    Math.round((1 - pkg.price / pkg.originalPrice) * 100) : 0;

                // 解析权益描述
                let benefits = [];
                try {
                    const benefitsData = JSON.parse(pkg.benefitsDesc || '{}');
                    benefits = benefitsData.items || [];
                } catch (e) {
                    benefits = [pkg.description || '高级会员权益'];
                }

                const planCard = document.createElement('div');
                planCard.className = 'plan-card' + (isRecommended ? ' featured' : '');

                let badgeHtml = '';
                if (isRecommended) {
                    badgeHtml = '<div class="plan-badge">' + (pkg.tag || '推荐') + '</div>';
                }

                let originalPriceHtml = '';
                if (pkg.originalPrice > pkg.price) {
                    originalPriceHtml = '<span class="original-price">¥' + pkg.originalPrice + '</span>';
                }

                let discountHtml = '';
                if (discountPercent > 0) {
                    discountHtml = '<div class="plan-discount">节省 ' + discountPercent + '%</div>';
                }

                const featuresHtml = benefits.map(benefit =>
                    '<div class="feature-item">' + benefit + '</div>'
                ).join('');

                planCard.innerHTML = badgeHtml +
                    '<h3>' + pkg.name + '</h3>' +
                    '<div class="plan-price">' +
                        '<span class="current-price">¥' + pkg.price + '</span>' +
                        originalPriceHtml +
                    '</div>' +
                    '<div class="plan-duration">' + pkg.duration + '天</div>' +
                    discountHtml +
                    '<div class="plan-features">' + featuresHtml + '</div>' +
                    '<button class="btn btn-select-plan" data-package-id="' + pkg.id + '" data-package-name="' + pkg.name + '" data-package-price="¥' + pkg.price + '">' +
                        '选择此套餐' +
                    '</button>';

                plansContainer.appendChild(planCard);
            });

            // 重新绑定选择套餐按钮事件
            bindSelectPlanEvents();
        }

        function bindSelectPlanEvents() {
            const selectPlanBtns = document.querySelectorAll('.btn-select-plan');
            selectPlanBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const packageId = this.getAttribute('data-package-id');
                    const packageName = this.getAttribute('data-package-name');
                    const packagePrice = this.getAttribute('data-package-price');

                    selectPlan(packageId, packageName, packagePrice);
                });
            });
        }

        let paymentPollingInterval = null;

        function showPayment() {
            showPage('payment-page');
            startCountdown();
            startPaymentStatusPolling();
        }

        function startPaymentStatusPolling() {
            // 清除之前的轮询
            if (paymentPollingInterval) {
                clearInterval(paymentPollingInterval);
            }

            const orderNo = document.getElementById('order-number').textContent;
            if (!orderNo) {
                return;
            }

            // 每10秒查询一次支付状态
            paymentPollingInterval = setInterval(async () => {
                try {
                    const response = await apiService.getPaymentStatus(orderNo);
                    const statusData = response.data || response;

                    if (statusData.status === 'PAID') {
                        // 支付成功，停止轮询
                        clearInterval(paymentPollingInterval);
                        paymentPollingInterval = null;

                        // 重新加载用户状态
                        await loadUserStatus();

                        // 显示成功消息
                        showToast('支付成功！会员已开通', 'success');

                        // 2秒后跳转到个人中心
                        setTimeout(() => {
                            showProfile();
                        }, 2000);

                    } else if (statusData.status === 'EXPIRED' || statusData.status === 'CANCELED') {
                        // 订单过期或取消，停止轮询
                        clearInterval(paymentPollingInterval);
                        paymentPollingInterval = null;

                        if (statusData.status === 'EXPIRED') {
                            showToast('订单已过期，请重新下单', 'error');
                        } else {
                            showToast('订单已取消', 'error');
                        }

                        setTimeout(() => {
                            showVipPlans();
                        }, 2000);
                    }
                    // PENDING状态继续轮询

                } catch (error) {
                    // 轮询失败不停止，继续尝试
                }
            }, 10000); // 每10秒查询一次

            // 5分钟后停止轮询（避免无限轮询）
            setTimeout(() => {
                if (paymentPollingInterval) {
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                }
            }, 300000); // 5分钟
        }

        function stopPaymentStatusPolling() {
            if (paymentPollingInterval) {
                clearInterval(paymentPollingInterval);
                paymentPollingInterval = null;
            }
        }

        function displayPaymentQrCode(orderResponse) {
            const qrCodeElement = document.querySelector('.qr-placeholder');
            if (!qrCodeElement) {
                return;
            }

            // 检查订单响应中是否包含二维码信息
            if (orderResponse.qrcodeUrl) {
                // 使用二维码URL显示图片
                const img = document.createElement('img');
                // 尝试将HTTP URL转换为HTTPS以提高安全性
                let qrcodeUrl = orderResponse.qrcodeUrl;
                if (qrcodeUrl.startsWith('http://images.yungouos.com')) {
                    qrcodeUrl = qrcodeUrl.replace('http://', 'https://');
                }
                img.src = qrcodeUrl;
                img.alt = '支付二维码';
                img.style.cssText = 'width: 100%; height: 100%; object-fit: contain;';

                // 添加图片加载错误处理
                img.onerror = function() {

                    // 创建错误显示容器
                    const errorDiv = document.createElement('div');
                    errorDiv.style.cssText = 'padding: 20px; text-align: center; border: 2px dashed #ccc; background: #f9f9f9;';

                    errorDiv.innerHTML =
                        '<p style="margin-bottom: 10px; font-weight: bold;">二维码加载失败</p>' +
                        '<p style="font-size: 12px; color: #666; margin-bottom: 10px;">请复制下方链接在浏览器中打开：</p>' +
                        '<p style="word-break: break-all; font-size: 11px; background: #fff; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">' +
                        qrcodeUrl + '</p>';

                    // 创建复制按钮
                    const copyBtn = document.createElement('button');
                    copyBtn.textContent = '复制链接';
                    copyBtn.style.cssText = 'margin-top: 10px; padding: 5px 10px; background: #007acc; color: white; border: none; border-radius: 3px; cursor: pointer;';
                    copyBtn.onclick = function() {
                        navigator.clipboard.writeText(qrcodeUrl).then(function() {
                            copyBtn.textContent = '已复制!';
                            setTimeout(function() {
                                copyBtn.textContent = '复制链接';
                            }, 2000);
                        }).catch(function() {
                            alert('复制失败，请手动复制链接');
                        });
                    };

                    errorDiv.appendChild(copyBtn);
                    qrCodeElement.innerHTML = '';
                    qrCodeElement.appendChild(errorDiv);
                };

                img.onload = function() {
                };

                qrCodeElement.innerHTML = '';
                qrCodeElement.appendChild(img);
            } else if (orderResponse.qrcodeContent) {
                // 如果有二维码内容但没有URL，可能需要生成二维码图片
                // 这里可以使用二维码生成库，暂时显示内容
                qrCodeElement.innerHTML = '<div style="padding: 20px; text-align: center; word-break: break-all; font-size: 12px;">' +
                    '<p>二维码内容:</p><p>' + orderResponse.qrcodeContent + '</p></div>';
            } else {
                // 如果创建订单时没有返回二维码，尝试刷新获取
                loadPaymentQrCode(orderResponse.orderNo);
            }
        }

        async function loadPaymentQrCode(orderNo) {
            try {
                const response = await apiService.refreshPaymentQrCode(orderNo);

                // API返回格式: {code, message, data, success}
                // data是Map<String, String>格式
                const qrCodeData = response.data || response;

                const qrCodeElement = document.querySelector('.qr-placeholder');
                if (!qrCodeElement) {
                    return;
                }

                // 检查不同可能的字段名
                let qrCodeUrl = null;
                if (qrCodeData) {
                    // 可能的字段名：qrCode, qrcodeUrl, qrCodeUrl, url等
                    qrCodeUrl = qrCodeData.qrCode || qrCodeData.qrcodeUrl || qrCodeData.qrCodeUrl ||
                               qrCodeData.url || qrCodeData.qrcodeContent || qrCodeData.content;
                }

                if (qrCodeUrl) {
                    if (qrCodeUrl.startsWith('http')) {
                        // 如果是URL，显示图片
                        // 尝试将HTTP URL转换为HTTPS以提高安全性
                        if (qrCodeUrl.startsWith('http://images.yungouos.com')) {
                            qrCodeUrl = qrCodeUrl.replace('http://', 'https://');
                        }

                        const img = document.createElement('img');
                        img.src = qrCodeUrl;
                        img.alt = '支付二维码';
                        img.style.cssText = 'width: 100%; height: 100%; object-fit: contain;';

                        // 添加图片加载错误处理
                        img.onerror = function() {

                            // 创建错误显示容器
                            const errorDiv = document.createElement('div');
                            errorDiv.style.cssText = 'padding: 20px; text-align: center; border: 2px dashed #ccc; background: #f9f9f9;';

                            errorDiv.innerHTML =
                                '<p style="margin-bottom: 10px; font-weight: bold;">二维码加载失败</p>' +
                                '<p style="font-size: 12px; color: #666; margin-bottom: 10px;">请复制下方链接在浏览器中打开：</p>' +
                                '<p style="word-break: break-all; font-size: 11px; background: #fff; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">' +
                                qrCodeUrl + '</p>';

                            // 创建复制按钮
                            const copyBtn = document.createElement('button');
                            copyBtn.textContent = '复制链接';
                            copyBtn.style.cssText = 'margin-top: 10px; padding: 5px 10px; background: #007acc; color: white; border: none; border-radius: 3px; cursor: pointer;';
                            copyBtn.onclick = function() {
                                navigator.clipboard.writeText(qrCodeUrl).then(function() {
                                    copyBtn.textContent = '已复制!';
                                    setTimeout(function() {
                                        copyBtn.textContent = '复制链接';
                                    }, 2000);
                                }).catch(function() {
                                    alert('复制失败，请手动复制链接');
                                });
                            };

                            errorDiv.appendChild(copyBtn);
                            qrCodeElement.innerHTML = '';
                            qrCodeElement.appendChild(errorDiv);
                        };

                        img.onload = function() {
                        };

                        qrCodeElement.innerHTML = '';
                        qrCodeElement.appendChild(img);
                    } else {
                        // 如果是内容，显示文本
                        qrCodeElement.innerHTML = '<div style="padding: 20px; text-align: center; word-break: break-all; font-size: 12px;">' +
                            '<p>二维码内容:</p><p>' + qrCodeUrl + '</p></div>';
                    }
                } else {
                    qrCodeElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #ff6b6b;">获取二维码失败，请重试</div>';
                    showToast('获取支付二维码失败', 'error');
                }

            } catch (error) {
                showToast('获取支付二维码失败', 'error');

                const qrCodeElement = document.querySelector('.qr-placeholder');
                if (qrCodeElement) {
                    qrCodeElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #ff6b6b;">网络错误，请重试</div>';
                }
            }
        }

        async function selectPlan(packageId, packageName, packagePrice) {
            if (!currentUser) {
                showToast('请先登录', 'error');
                showAuth();
                return;
            }

            // 使用API返回的实际套餐数据
            const membershipTypeId = parseInt(packageId);
            const productName = packageName;
            const amount = packagePrice;

            showToast('创建订单中...', 'info');

            try {
                const response = await apiService.createOrder(membershipTypeId, 'alipay');

                // API返回格式: {code, message, data, success}
                // 需要提取data字段中的实际订单数据
                const orderResponse = response.data || response;

                // 更新订单信息显示
                document.getElementById('order-product').textContent = productName;
                document.getElementById('order-amount').textContent = amount;
                document.getElementById('order-number').textContent = orderResponse.orderNo;

                // 显示支付二维码
                displayPaymentQrCode(orderResponse);

                showPayment();

            } catch (error) {
                let errorMessage = '创建订单失败';

                if (error.message.includes('余额不足')) {
                    errorMessage = '账户余额不足';
                } else if (error.message.includes('套餐不存在')) {
                    errorMessage = '选择的套餐不存在';
                } else {
                    errorMessage = error.message || errorMessage;
                }

                showToast(errorMessage, 'error');
            }
        }

        function generateOrderNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hour = String(now.getHours()).padStart(2, '0');
            const minute = String(now.getMinutes()).padStart(2, '0');
            const second = String(now.getSeconds()).padStart(2, '0');
            const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

            return year + month + day + hour + minute + second + random;
        }

        function startCountdown() {
            let timeLeft = 5 * 60;

            const countdownElement = document.getElementById('countdown');

            const timer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;

                countdownElement.textContent = minutes + ':' + seconds.toString().padStart(2, '0');

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    countdownElement.textContent = '已过期';
                }

                timeLeft--;
            }, 1000);
        }

        async function confirmPayment() {
            const orderNo = document.getElementById('order-number').textContent;

            if (!orderNo) {
                showToast('订单信息异常', 'error');
                return;
            }

            showToast('查询支付状态中...', 'info');

            try {
                // 调用支付状态查询API
                const response = await apiService.getPaymentStatus(orderNo);
                const statusData = response.data || response;

                // 检查订单状态
                if (statusData.status === 'PAID') {
                    // 支付成功，重新加载用户状态
                    await loadUserStatus();
                    showToast('支付成功！会员已开通', 'success');
                    setTimeout(() => {
                        showProfile();
                    }, 2000);
                } else if (statusData.status === 'PENDING') {
                    showToast('订单尚未支付，请完成支付后再试', 'warning');
                } else if (statusData.status === 'EXPIRED') {
                    showToast('订单已过期，请重新下单', 'error');
                    setTimeout(() => {
                        showVipPlans();
                    }, 2000);
                } else if (statusData.status === 'CANCELED') {
                    showToast('订单已取消', 'error');
                    setTimeout(() => {
                        showVipPlans();
                    }, 2000);
                } else {
                    showToast('订单状态异常: ' + statusData.status, 'error');
                }

            } catch (error) {
                showToast('查询支付状态失败，请稍后再试', 'error');
            }
        }

        async function cancelPayment() {
            const orderNo = document.getElementById('order-number').textContent;

            if (orderNo && orderNo !== '202508021808302820006') { // 不是默认订单号
                try {
                    await apiService.cancelOrder(orderNo);
                    showToast('订单已取消');
                } catch (error) {
                    showToast('取消订单失败', 'error');
                }
            }

            showVipPlans();
        }

        async function getEmail() {
            if (!currentUser) {
                showToast('请先登录', 'error');
                showAuth();
                return;
            }

            // 检查VIP权限
            if (!currentUser.isVip) {
                showToast('仅VIP用户可使用邮箱池功能', 'error');
                showUpgrade();
                return;
            }

            showToast('获取邮箱中...', 'info');

            try {
                // 调用邮箱池API获取邮箱
                const response = await apiService.requestEmail();

                // 检查API响应是否成功
                if (response && response.success && response.data && response.data.emailAddress) {
                    // API返回格式：{code: 200, message: "邮箱分配成功", data: {emailAddress: "<EMAIL>", expiresIn: 345600, dailyRemaining: 4}, success: true}
                    currentEmail = response.data.emailAddress;
                    const expiresIn = response.data.expiresIn;
                    const dailyRemaining = response.data.dailyRemaining;

                    document.getElementById('current-email').textContent = currentEmail;
                    document.getElementById('copy-email-btn').disabled = false;

                    // 显示邮箱信息和剩余次数
                    const expiresHours = Math.floor(expiresIn / 3600);
                    showToast('邮箱获取成功！' );

                    // 清空之前的验证码和状态
                    currentVerificationCode = null;
                    resetVerificationUI();

                    // 启用"已发送验证码"按钮
                    const sendCodeBtn = document.getElementById('email-verification-btn');
                    if (sendCodeBtn) {
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '已发送验证码';
                    }

                } else {
                    // API返回了错误，抛出包含错误码和消息的错误
                    const errorCode = response.code || 'unknown';
                    const errorMessage = response.message || 'API响应格式错误';
                    throw new Error(errorCode + ': ' + errorMessage);
                }

            } catch (error) {

                // 处理特定错误码
                if (error.message.includes('12100')) {
                    showToast('仅VIP用户可使用邮箱池功能', 'error');
                    showUpgrade();
                } else if (error.message.includes('12101')) {
                    showToast('邮箱池已耗尽，请稍后重试', 'error');
                } else if (error.message.includes('12102')) {
                    showToast('今日邮箱使用次数已达上限', 'error');
                } else {
                    showToast('获取邮箱失败: ' + error.message, 'error');
                }
            }
        }

        function copyEmail() {
            if (currentEmail) {
                navigator.clipboard.writeText(currentEmail).then(() => {
                    showToast('邮箱已复制到剪贴板');
                }).catch(() => {
                    showToast('复制失败', 'error');
                });
            }
        }

        function copyCurrentCode() {
            if (currentVerificationCode) {
                navigator.clipboard.writeText(currentVerificationCode.code).then(() => {
                    showToast('验证码已复制到剪贴板');
                }).catch(() => {
                    showToast('复制失败', 'error');
                });
            }
        }

        // 重置验证码UI
        function resetVerificationUI() {
            document.getElementById('verification-code-single').innerHTML =
                '<div class="empty-state">' +
                    '<div class="empty-icon">📭</div>' +
                    '<p>暂无验证码，点击"已发送验证码"开始检测</p>' +
                '</div>';
        }

        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
            isPolling = false;
            pollingStartTime = null;
        }

        // 停止倒计时
        function stopCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            buttonCooldownTime = null;
        }

        // 格式化时间显示（MM:SS）
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            const paddedSeconds = remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds.toString();
            return minutes + ':' + paddedSeconds;
        }

        // 已发送验证码按钮点击处理
        async function sendVerificationCode() {

            if (!currentUser) {
                showToast('请先登录', 'error');
                return;
            }

            if (!currentEmail) {
                showToast('请先获取邮箱', 'error');
                return;
            }

            if (isPolling) {
                showToast('验证码检测正在进行中，请等待完成', 'warning');
                return;
            }

            const sendBtn = document.getElementById('email-verification-btn');
            if (!sendBtn) {
                return;
            }

            try {
                // 立即调用start-check接口
                showToast('开始检测验证码...', 'info');
                const response = await apiService.startVerificationCheck(currentEmail);

                if (response && response.success) {
                    showToast('验证码检测已启动', 'success');

                    // 禁用按钮并开始倒计时
                    sendBtn.disabled = true;
                    buttonCooldownTime = Date.now() + 120000; // 2分钟后

                    // 开始倒计时显示
                    startButtonCountdown();

                    // 延迟5秒后启动轮询
                    setTimeout(() => {
                        startPolling();
                    }, 5000);

                } else {
                    throw new Error(response.message || '启动检测失败');
                }

            } catch (error) {

                // 处理特定错误码
                if (error.message.includes('12104')) {
                    showToast('邮箱未分配给当前用户', 'error');
                } else {
                    showToast('启动检测失败: ' + error.message, 'error');
                }
            }
        }

        // 开始按钮倒计时
        function startButtonCountdown() {
            const sendBtn = document.getElementById('email-verification-btn');
            if (!sendBtn) return;

            // 添加倒计时样式
            sendBtn.classList.add('btn-countdown');

            countdownInterval = setInterval(() => {
                const now = Date.now();
                const remaining = Math.max(0, Math.ceil((buttonCooldownTime - now) / 1000));

                if (remaining > 0) {
                    sendBtn.textContent = '已发送 (' + formatTime(remaining) + ')';
                } else {
                    // 倒计时结束
                    sendBtn.textContent = '已发送验证码';
                    sendBtn.disabled = false;
                    sendBtn.classList.remove('btn-countdown');
                    stopCountdown();
                }
            }, 1000);
        }

        // 开始轮询
        function startPolling() {
            if (isPolling || !currentEmail) {
                return;
            }

            isPolling = true;
            pollingStartTime = Date.now();

            // 立即执行一次查询
            checkEmailStatus();

            // 每7秒轮询一次
            pollingInterval = setInterval(() => {
                const elapsed = Date.now() - pollingStartTime;

                // 2分钟超时
                if (elapsed >= 120000) {
                    stopPolling();
                    showToast('验证码检测超时，请重试', 'warning');
                    return;
                }

                checkEmailStatus();
            }, 7000);
        }

        // 检查邮箱状态
        async function checkEmailStatus() {
            if (!currentEmail) {
                return;
            }

            try {
                const response = await apiService.getEmailStatus(currentEmail);

                if (response && response.data) {
                    const statusData = response.data;

                    // 检查是否找到验证码
                    if (statusData.checkStatus === 'found' && statusData.verificationCode) {
                        // 找到验证码，停止轮询
                        stopPolling();

                        // 显示验证码
                        displayVerificationCodeFromStatus(statusData);
                        showToast('验证码获取成功！', 'success');

                    } else if (statusData.checkStatus === 'timeout') {
                        // 检测超时
                        stopPolling();
                        showToast('验证码检测超时，请重试', 'warning');

                    } else if (statusData.checkStatus === 'checking') {
                        // 仍在检测中，继续等待

                    } else {
                        // 其他状态
                    }
                }

            } catch (error) {

                // 如果是严重错误，停止轮询
                if (error.message.includes('12104')) {
                    stopPolling();
                    showToast('邮箱未分配给当前用户', 'error');
                }
            }
        }

        // 从状态数据显示验证码
        function displayVerificationCodeFromStatus(statusData) {
            const time = new Date(statusData.checkStartedAt || Date.now()).toLocaleTimeString();

            currentVerificationCode = {
                code: statusData.verificationCode,
                time: time,
                subject: '验证码'
            };

            const codeContainer = document.getElementById('verification-code-single');
            if (codeContainer) {
                codeContainer.innerHTML =
                    '<div class="code-item">' +
                        '<div class="code-content">' +
                            '<div class="code-text">' + currentVerificationCode.code + '</div>' +
                            '<div class="code-time">收到时间: ' + currentVerificationCode.time + '</div>' +
                        '</div>' +
                        '<div class="code-actions">' +
                            '<button class="btn btn-copy" id="copy-verification-code-btn">复制</button>' +
                        '</div>' +
                    '</div>';

                // 为新创建的复制按钮添加事件监听器
                const copyCodeBtn = document.getElementById('copy-verification-code-btn');
                if (copyCodeBtn) {
                    copyCodeBtn.addEventListener('click', copyCurrentCode);
                }
            }
        }

        async function refreshCodes() {
            if (!currentUser) {
                showToast('请先登录', 'error');
                return;
            }

            if (!currentEmail) {
                showToast('请先获取邮箱', 'error');
                return;
            }

            // 如果轮询正在进行中，不能启动新的轮询，但可以手动查询一次
            if (isPolling) {
                showToast('正在自动检测中，手动查询状态...', 'info');
            } else {
                showToast('刷新中...', 'info');
            }

            try {
                // 调用邮箱池状态查询API
                const response = await apiService.getEmailStatus(currentEmail);

                if (response && response.data) {
                    const statusData = response.data;

                    if (statusData.checkStatus === 'found' && statusData.verificationCode) {
                        // 找到验证码
                        displayVerificationCodeFromStatus(statusData);
                        showToast('刷新完成，找到验证码！', 'success');

                        // 如果正在轮询，停止轮询
                        if (isPolling) {
                            stopPolling();
                        }

                    } else if (statusData.checkStatus === 'checking') {
                        showToast('验证码检测中，请稍后再试', 'info');

                    } else if (statusData.checkStatus === 'timeout') {
                        showToast('验证码检测已超时', 'warning');

                    } else if (statusData.status === 'assigned') {
                        showToast('邮箱已分配，请先点击"已发送验证码"开始检测', 'info');

                    } else {
                        showToast('暂无验证码', 'info');
                    }
                } else {
                    throw new Error('API响应格式错误');
                }

            } catch (error) {

                // 处理特定错误码
                if (error.message.includes('12104')) {
                    showToast('邮箱未分配给当前用户', 'error');
                } else {
                    showToast('刷新失败: ' + error.message, 'error');
                }
            }
        }

        // 显示验证码
        function displayVerificationCode(message) {
            const time = new Date(message.time || Date.now()).toLocaleTimeString();

            // 从消息内容中提取验证码（简单的正则匹配）
            const codeMatch = message.content.match(/\b\d{4,6}\b/);
            const code = codeMatch ? codeMatch[0] : message.content.substring(0, 6);

            currentVerificationCode = {
                code: code,
                time: time,
                subject: message.subject || '验证码',
                content: message.content
            };

            document.getElementById('verification-code-single').innerHTML =
                '<div class="code-item">' +
                    '<div class="code-content">' +
                        '<div class="code-text">' + currentVerificationCode.code + '</div>' +
                        '<div class="code-time">收到时间: ' + currentVerificationCode.time + '</div>' +
                    '</div>' +
                    '<div class="code-actions">' +
                        '<button class="btn btn-copy" id="copy-display-code-btn">复制</button>' +
                    '</div>' +
                '</div>';

            // 为新创建的复制按钮添加事件监听器
            const copyCodeBtn = document.getElementById('copy-display-code-btn');
            if (copyCodeBtn) {
                copyCodeBtn.addEventListener('click', copyCurrentCode);
            }
        }

        function generateNewVerificationCode() {
            const code = Math.random().toString().substr(2, 6);
            const time = new Date().toLocaleTimeString();

            currentVerificationCode = {
                code: code,
                time: time,
                id: Date.now()
            };

            updateVerificationCodeUI();
        }

        function updateVerificationCodeUI() {
            const container = document.getElementById('verification-code-single');

            if (!currentVerificationCode) {
                container.innerHTML =
                    '<div class="empty-state">' +
                        '<div class="empty-icon">📭</div>' +
                        '<p>暂无验证码</p>' +
                    '</div>';
                return;
            }

            container.innerHTML =
                '<div class="code-item">' +
                    '<div class="code-content">' +
                        '<div class="code-text">' + currentVerificationCode.code + '</div>' +
                        '<div class="code-time">收到时间: ' + currentVerificationCode.time + '</div>' +
                    '</div>' +
                    '<div class="code-actions">' +
                        '<button class="btn btn-copy" id="copy-code-btn">复制</button>' +
                    '</div>' +
                '</div>';

            // 为新创建的复制按钮添加事件监听器
            const copyCodeBtn = document.getElementById('copy-code-btn');
            if (copyCodeBtn) {
                copyCodeBtn.addEventListener('click', copyCurrentCode);
            }
        }

        async function resetPlugin() {
            // 首先验证会员有效性
            if (!currentUser) {
                showToast('请先登录', 'error');
                showAuth();
                return;
            }

            // 检查VIP权限
            if (!currentUser.isVip) {
                showToast('仅VIP用户可使用重置插件功能', 'error');
                return;
            }

            // 检查VIP是否有效（未过期）
            if (!isVipValid()) {
                showToast('VIP已过期，请续费后使用重置功能', 'error');
                return;
            }

            // 会员验证通过，执行重置操作
            // 发送消息到后端获取宿主信息
            if (vscodeApi) {
                vscodeApi.postMessage({
                    command: 'resetPlugin'
                });
            }

            // 显示提示信息
            showToast('宿主信息已输出到控制台，请查看VS Code输出面板', 'info');

            // 直接重置插件数据，不使用confirm对话框
            showToast('同时重置插件数据...', 'info');

            // 停止所有轮询和倒计时
            stopPolling();
            stopCountdown();

            setTimeout(() => {
                currentEmail = null;
                currentVerificationCode = null;

                document.getElementById('current-email').textContent = '点击②  获取邮箱按钮生成邮箱地址';
                document.getElementById('copy-email-btn').disabled = true;

                // 重置邮箱验证码按钮
                const emailVerificationBtn = document.getElementById('email-verification-btn');
                if (emailVerificationBtn) {
                    emailVerificationBtn.disabled = true;
                    emailVerificationBtn.textContent = '已发送验证码';
                }

                resetVerificationUI();

                showToast('插件数据已重置，宿主信息请查看VS Code控制台');

                // 重置完成提示，后端会自动处理进程关闭
                showToast('重置完成，后端将自动处理宿主进程', 'success');
            }, 1000);
        }

        // 页面初始化
        function initializePage() {
            // 加载允许的邮箱域名
            loadAllowedEmailDomains();

            // 加载系统公告
            loadAnnouncements();

            // 尝试从本地存储恢复认证信息
            try {
                const savedToken = localStorage.getItem('authToken');
                const savedRefreshToken = localStorage.getItem('refreshToken');
                const savedUser = localStorage.getItem('currentUser');

                if (savedToken && savedUser) {
                    authToken = savedToken;
                    refreshToken = savedRefreshToken;
                    currentUser = JSON.parse(savedUser);

                    apiService.setToken(authToken);

                    // 验证token是否仍然有效
                    loadUserStatus().then(() => {
                        updateLoginStatus(true, currentUser.email);
                        updateProfileInfo();
                        showToast('欢迎回来！');
                    }).catch((error) => {
                        // Token无效，清除本地存储
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('refreshToken');
                        localStorage.removeItem('currentUser');
                        authToken = null;
                        refreshToken = null;
                        currentUser = null;
                        apiService.setToken(null);
                    });
                }
            } catch (error) {
            }
        }



        // 保存认证信息到本地存储
        function saveAuthInfo() {
            if (authToken && currentUser) {
                localStorage.setItem('authToken', authToken);
                if (refreshToken) {
                    localStorage.setItem('refreshToken', refreshToken);
                }
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            }
        }

        // 清除本地存储的认证信息
        function clearAuthInfo() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('currentUser');
        }

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后初始化
            initializePage();
            // 登录按钮
            const loginBtn = document.getElementById('login-btn');
            if (loginBtn) {
                loginBtn.addEventListener('click', showAuth);
            }

            // 认证页面按钮
            const loginSubmitBtn = document.getElementById('login-submit-btn');
            if (loginSubmitBtn) {
                loginSubmitBtn.addEventListener('click', login);
            }

            const registerSubmitBtn = document.getElementById('register-submit-btn');
            if (registerSubmitBtn) {
                registerSubmitBtn.addEventListener('click', register);
            }

            const showRegisterLink = document.getElementById('show-register-link');
            if (showRegisterLink) {
                showRegisterLink.addEventListener('click', showRegister);
            }

            const showLoginLink = document.getElementById('show-login-link');
            if (showLoginLink) {
                showLoginLink.addEventListener('click', showLogin);
            }

            const backToMainBtn = document.getElementById('back-to-main-btn');
            if (backToMainBtn) {
                backToMainBtn.addEventListener('click', showMain);
            }

            const sendVerificationBtn = document.getElementById('send-verification-btn');
            if (sendVerificationBtn) {
                sendVerificationBtn.addEventListener('click', sendVerificationCodeForRegister);
            }

            // 邮箱验证码检测按钮
            const emailVerificationBtn = document.getElementById('email-verification-btn');
            if (emailVerificationBtn) {
                emailVerificationBtn.addEventListener('click', sendVerificationCode);
            }

            const profileBtn = document.getElementById('profile-btn');
            if (profileBtn) {
                profileBtn.addEventListener('click', showProfile);
            }

            const tutorialBtn = document.getElementById('tutorial-btn');
            if (tutorialBtn) {
                tutorialBtn.addEventListener('click', openTutorial);
            }



            // 个人中心页面按钮
            const profileBackBtn = document.getElementById('profile-back-btn');
            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', showMain);
            }

            const upgradeBtn = document.getElementById('upgrade-btn');
            if (upgradeBtn) {
                upgradeBtn.addEventListener('click', showUpgrade);
            }

            const introductionItem = document.getElementById('introduction-item');
            if (introductionItem) {
                introductionItem.addEventListener('click', showIntroduction);
            }

            const contactItem = document.getElementById('contact-item');
            if (contactItem) {
                contactItem.addEventListener('click', showContact);
            }

            // 介绍弹框事件监听器
            const introductionModalClose = document.getElementById('introduction-modal-close');
            if (introductionModalClose) {
                introductionModalClose.addEventListener('click', hideIntroductionModal);
            }

            const introductionModal = document.getElementById('introduction-modal');
            if (introductionModal) {
                introductionModal.addEventListener('click', function(e) {
                    if (e.target === introductionModal) {
                        hideIntroductionModal();
                    }
                });
            }

            // 联系我弹框事件监听器
            const contactModalClose = document.getElementById('contact-modal-close');
            if (contactModalClose) {
                contactModalClose.addEventListener('click', hideContactModal);
            }

            const copyWechatBtn = document.getElementById('copy-wechat-btn');
            if (copyWechatBtn) {
                copyWechatBtn.addEventListener('click', copyWechatId);
            }

            // 点击弹框背景关闭弹框
            const contactModal = document.getElementById('contact-modal');
            if (contactModal) {
                contactModal.addEventListener('click', function(e) {
                    if (e.target === contactModal) {
                        hideContactModal();
                    }
                });
            }



            const logoutItem = document.getElementById('logout-item');
            if (logoutItem) {
                logoutItem.addEventListener('click', logout);
            }

            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', logout);
            }

            // VIP套餐页面按钮
            const vipPlansBackBtn = document.getElementById('vip-plans-back-btn');
            if (vipPlansBackBtn) {
                vipPlansBackBtn.addEventListener('click', showProfile);
            }

            // 套餐选择按钮事件将通过 bindSelectPlanEvents() 动态绑定

            // 支付页面按钮
            const paymentBackBtn = document.getElementById('payment-back-btn');
            if (paymentBackBtn) {
                paymentBackBtn.addEventListener('click', showVipPlans);
            }

            const confirmPaymentBtn = document.getElementById('confirm-payment-btn');
            if (confirmPaymentBtn) {
                confirmPaymentBtn.addEventListener('click', confirmPayment);
            }

            const cancelPaymentBtn = document.getElementById('cancel-payment-btn');
            if (cancelPaymentBtn) {
                cancelPaymentBtn.addEventListener('click', cancelPayment);
            }

            // 获取邮箱按钮
            const getEmailBtn = document.getElementById('get-email-btn');
            if (getEmailBtn) {
                getEmailBtn.addEventListener('click', getEmail);
            }

            // 复制邮箱按钮
            const copyEmailBtn = document.getElementById('copy-email-btn');
            if (copyEmailBtn) {
                copyEmailBtn.addEventListener('click', copyEmail);
            }

            // 刷新验证码按钮
            const refreshCodesBtn = document.getElementById('refresh-codes-btn');
            if (refreshCodesBtn) {
                refreshCodesBtn.addEventListener('click', refreshCodes);
            }

            // 重置插件按钮
            const resetPluginBtn = document.getElementById('reset-plugin-btn');
            if (resetPluginBtn) {
                resetPluginBtn.addEventListener('click', resetPlugin);
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            stopPolling();
            stopCountdown();
        });
    </script>
</body>
</html>`;
	}

	// 实例方法：调用静态方法
	private getHtmlContent(nonce: string): string {
		return TempMailPanel.generateHtmlContent(nonce);
	}

	// 禁用遥测系统 - 防止VS Code重写storage.json
	private async disableTelemetry(): Promise<void> {
		try {
			console.log('🔍 disableTelemetry - 开始禁用遥测系统');

			// 获取当前配置
			const config = vscode.workspace.getConfiguration();

			// 设置遥测级别为关闭
			await config.update('telemetry.telemetryLevel', 'off', vscode.ConfigurationTarget.Global);

			console.log('🔍 disableTelemetry - 遥测系统已禁用');
			vscode.window.showInformationMessage('已禁用遥测系统，防止配置被覆盖');

		} catch (error) {
			console.log('🔍 disableTelemetry - 禁用失败:', error);
			vscode.window.showWarningMessage('禁用遥测系统失败，配置可能会被覆盖');
		}
	}

	// 检查是否有多个VS Code窗口运行
	private async checkMultipleWindows(): Promise<boolean> {
		try {
			console.log('🔍 checkMultipleWindows - 检查多窗口');

			const { exec } = require('child_process');
			const { promisify } = require('util');
			const execAsync = promisify(exec);

			// 检查VS Code进程数量
			const { stdout } = await execAsync('ps aux | grep -E "Visual Studio Code|Code Helper" | grep -v grep | wc -l');
			const processCount = parseInt(stdout.trim());

			console.log('🔍 checkMultipleWindows - VS Code进程数量:', processCount);

			// 如果进程数量大于8，可能有多个窗口
			const hasMultipleWindows = processCount > 8;

			if (hasMultipleWindows) {
				console.log('⚠️ 检测到可能有多个VS Code窗口运行');
			}

			return hasMultipleWindows;

		} catch (error) {
			console.log('🔍 checkMultipleWindows - 检查失败:', error);
			return false; // 检查失败时假设没有多窗口
		}
	}
}

function getNonce() {
	let text = '';
	const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	for (let i = 0; i < 32; i++) {
		text += possible.charAt(Math.floor(Math.random() * possible.length));
	}
	return text;
}
